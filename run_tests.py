#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试执行脚本
支持多种测试模式和配置选项
"""

import os
import sys
import argparse
import subprocess
from datetime import datetime
from pathlib import Path


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_dir = self.project_root / "tests"
        self.result_dir = self.project_root / "result" / "test_results"
        self.result_dir.mkdir(parents=True, exist_ok=True)
        
    def run_preconditions_tests(self, verbose=True):
        """运行前置条件测试"""
        print("=" * 60)
        print("运行前置条件测试")
        print("=" * 60)
        
        cmd = [
            "python", "-m", "pytest",
            "tests/test_preconditions.py",
            "tests/test_preconditions_integration.py",
            "-v" if verbose else "",
            "--tb=short",
            f"--html={self.result_dir}/preconditions_report.html",
            "--self-contained-html"
        ]
        
        return self._execute_command(cmd, "前置条件测试")
    
    def run_futures_cases_tests(self, verbose=True):
        """运行期货案例测试"""
        print("=" * 60)
        print("运行期货案例测试")
        print("=" * 60)
        
        html_path = str(self.result_dir / "futures_cases_report.html")
        cmd = [
            "python", "-m", "pytest",
            "tests/test_futures_cases.py",
            "-v" if verbose else "",
            "--tb=short",
            "--html=" + html_path,
            "--self-contained-html"
        ]
        
        return self._execute_command(cmd, "期货案例测试")
    
    def run_query_functions_tests(self, verbose=True):
        """运行查询功能测试"""
        print("=" * 60)
        print("运行查询功能测试")
        print("=" * 60)
        
        html_path = str(self.result_dir / "query_functions_report.html")
        cmd = [
            "python", "-m", "pytest",
            "tests/test_query_functions.py",
            "-v" if verbose else "",
            "--tb=short",
            "--html=" + html_path,
            "--self-contained-html"
        ]
        
        return self._execute_command(cmd, "查询功能测试")
    
    def run_trading_scenarios_tests(self, verbose=True):
        """运行交易场景测试"""
        print("=" * 60)
        print("运行交易场景测试")
        print("=" * 60)
        
        html_path = str(self.result_dir / "trading_scenarios_report.html")
        cmd = [
            "python", "-m", "pytest",
            "tests/test_trading_scenarios.py",
            "-v" if verbose else "",
            "--tb=short",
            "--html=" + html_path,
            "--self-contained-html"
        ]
        
        return self._execute_command(cmd, "交易场景测试")
    
    def run_integration_tests(self, verbose=True):
        """运行集成测试"""
        print("=" * 60)
        print("运行集成测试")
        print("=" * 60)
        
        html_path = str(self.result_dir / "integration_report.html")
        cmd = [
            "python", "-m", "pytest",
            "tests/",
            "-m", "integration",
            "-v" if verbose else "",
            "--tb=short",
            "--html=" + html_path,
            "--self-contained-html"
        ]
        
        return self._execute_command(cmd, "集成测试")
    
    def run_all_tests(self, verbose=True):
        """运行所有测试"""
        print("=" * 60)
        print("运行所有测试")
        print("=" * 60)
        
        html_path = str(self.result_dir / "all_tests_report.html")
        junit_path = str(self.result_dir / "junit_report.xml")
        cmd = [
            "python", "-m", "pytest",
            "tests/",
            "-v" if verbose else "",
            "--tb=short",
            "--html=" + html_path,
            "--self-contained-html",
            "--junitxml=" + junit_path
        ]
        
        return self._execute_command(cmd, "所有测试")
    
    def run_specific_test(self, test_path, verbose=True):
        """运行特定测试"""
        print("=" * 60)
        print(f"运行特定测试: {test_path}")
        print("=" * 60)
        
        test_name = Path(test_path).stem
        html_path = str(self.result_dir / f"{test_name}_report.html")
        cmd = [
            "python", "-m", "pytest",
            test_path,
            "-v" if verbose else "",
            "--tb=short",
            "--html=" + html_path,
            "--self-contained-html"
        ]
        
        return self._execute_command(cmd, f"特定测试 {test_path}")
    
    def run_parametrized_test(self, test_class, test_method, params=None, verbose=True):
        """运行参数化测试"""
        test_selector = f"{test_class}::{test_method}"
        if params:
            test_selector += f"[{params}]"
        
        print("=" * 60)
        print(f"运行参数化测试: {test_selector}")
        print("=" * 60)
        
        cmd = [
            "python", "-m", "pytest",
            f"tests/{test_class}.py::{test_selector}",
            "-v" if verbose else "",
            "--tb=short"
        ]
        
        return self._execute_command(cmd, f"参数化测试 {test_selector}")
    
    def run_smoke_tests(self, verbose=True):
        """运行冒烟测试"""
        print("=" * 60)
        print("运行冒烟测试")
        print("=" * 60)
        
        # 选择关键测试用例进行快速验证
        smoke_tests = [
            "tests/test_preconditions.py::TestPreconditions::test_precond_001_account_fund_sufficient",
            "tests/test_preconditions.py::TestPreconditions::test_precond_005_system_normal",
            "tests/test_query_functions.py::TestQueryFunctions::test_query_trading_account",
            "tests/test_futures_cases.py::TestFuturesCases::test_tc_fu001_stock_index_futures_buy_open"
        ]
        
        html_path = str(self.result_dir / "smoke_tests_report.html")
        cmd = [
            "python", "-m", "pytest"
        ] + smoke_tests + [
            "-v" if verbose else "",
            "--tb=short",
            "--html=" + html_path,
            "--self-contained-html"
        ]
        
        return self._execute_command(cmd, "冒烟测试")
    
    def _execute_command(self, cmd, test_name):
        """执行命令"""
        # 过滤空字符串
        cmd = [arg for arg in cmd if arg]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("-" * 60)
        
        start_time = datetime.now()
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            # 确保项目根目录在Python路径中
            current_pythonpath = env.get('PYTHONPATH', '')
            project_paths = [
                str(self.project_root),
                str(self.project_root / "trader"),
                str(self.project_root / "tests")
            ]
            
            if current_pythonpath:
                env['PYTHONPATH'] = f"{':'.join(project_paths)}:{current_pythonpath}"
            else:
                env['PYTHONPATH'] = ':'.join(project_paths)
            
            print(f"PYTHONPATH: {env['PYTHONPATH']}")
            
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                env=env,
                capture_output=False,
                text=True
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            if result.returncode == 0:
                print(f"\n✓ {test_name}执行成功，耗时: {duration:.2f}秒")
                return True
            else:
                print(f"\n✗ {test_name}执行失败，返回码: {result.returncode}，耗时: {duration:.2f}秒")
                return False
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            print(f"\n✗ {test_name}执行异常: {e}，耗时: {duration:.2f}秒")
            return False
    
    def generate_summary_report(self):
        """生成汇总报告"""
        print("\n" + "=" * 60)
        print("生成测试汇总报告")
        print("=" * 60)
        
        report_files = list(self.result_dir.glob("*.html"))
        
        if not report_files:
            print("未找到测试报告文件")
            return
        
        summary_content = self._create_summary_html(report_files)
        summary_file = self.result_dir / "test_summary.html"
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        print(f"✓ 汇总报告已生成: {summary_file}")
        print(f"✓ 测试结果目录: {self.result_dir}")
    
    def _create_summary_html(self, report_files):
        """创建汇总HTML"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>CTP期货交易测试汇总报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .report-list {{ margin-top: 20px; }}
        .report-item {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 3px; }}
        .report-link {{ color: #0066cc; text-decoration: none; font-weight: bold; }}
        .report-link:hover {{ text-decoration: underline; }}
        .timestamp {{ color: #666; font-size: 0.9em; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>CTP期货交易测试汇总报告</h1>
        <p class="timestamp">生成时间: {timestamp}</p>
    </div>
    
    <div class="report-list">
        <h2>测试报告列表</h2>
"""
        
        for report_file in sorted(report_files):
            report_name = report_file.stem.replace('_', ' ').title()
            html_content += f"""
        <div class="report-item">
            <a href="{report_file.name}" class="report-link">{report_name}</a>
            <p>文件: {report_file.name}</p>
        </div>
"""
        
        html_content += """
    </div>
    
    <div style="margin-top: 30px; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
        <h3>使用说明</h3>
        <ul>
            <li>点击上方链接查看具体的测试报告</li>
            <li>每个报告包含详细的测试结果和错误信息</li>
            <li>建议按顺序查看：前置条件 → 查询功能 → 期货案例 → 交易场景</li>
        </ul>
    </div>
</body>
</html>
"""
        
        return html_content


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CTP期货交易测试执行脚本")
    
    parser.add_argument(
        "test_type",
        choices=[
            "preconditions", "futures_cases", "query_functions", 
            "trading_scenarios", "integration", "all", "smoke", "specific"
        ],
        help="测试类型"
    )
    
    parser.add_argument(
        "--test-path",
        help="特定测试路径（当test_type为specific时使用）"
    )
    
    parser.add_argument(
        "--test-class",
        help="测试类名（用于参数化测试）"
    )
    
    parser.add_argument(
        "--test-method", 
        help="测试方法名（用于参数化测试）"
    )
    
    parser.add_argument(
        "--params",
        help="测试参数（用于参数化测试）"
    )
    
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="静默模式，减少输出"
    )
    
    parser.add_argument(
        "--no-report",
        action="store_true", 
        help="不生成汇总报告"
    )
    
    args = parser.parse_args()
    
    runner = TestRunner()
    verbose = not args.quiet
    success = False
    
    try:
        if args.test_type == "preconditions":
            success = runner.run_preconditions_tests(verbose)
        elif args.test_type == "futures_cases":
            success = runner.run_futures_cases_tests(verbose)
        elif args.test_type == "query_functions":
            success = runner.run_query_functions_tests(verbose)
        elif args.test_type == "trading_scenarios":
            success = runner.run_trading_scenarios_tests(verbose)
        elif args.test_type == "integration":
            success = runner.run_integration_tests(verbose)
        elif args.test_type == "all":
            success = runner.run_all_tests(verbose)
        elif args.test_type == "smoke":
            success = runner.run_smoke_tests(verbose)
        elif args.test_type == "specific":
            if not args.test_path:
                print("错误: 特定测试需要指定 --test-path")
                sys.exit(1)
            success = runner.run_specific_test(args.test_path, verbose)
        
        # 生成汇总报告
        if not args.no_report:
            runner.generate_summary_report()
        
        if success:
            print(f"\n🎉 测试执行成功!")
            sys.exit(0)
        else:
            print(f"\n❌ 测试执行失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n⚠️  测试被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
