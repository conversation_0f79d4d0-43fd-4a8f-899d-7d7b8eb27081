#!/bin/bash
# CTP期货交易测试执行脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$PROJECT_ROOT"

# 设置Python路径
export PYTHONPATH="$PROJECT_ROOT:$PROJECT_ROOT/trader"

# 创建结果目录
mkdir -p result/test_results

echo -e "${BLUE}CTP期货交易测试执行脚本${NC}"
echo "=================================="

# 显示使用帮助
show_help() {
    echo "使用方法: $0 [选项] [测试类型]"
    echo ""
    echo "测试类型:"
    echo "  preconditions    - 前置条件测试"
    echo "  query           - 查询功能测试"
    echo "  futures         - 期货案例测试"
    echo "  scenarios       - 交易场景测试"
    echo "  integration     - 集成测试"
    echo "  smoke           - 冒烟测试"
    echo "  all             - 所有测试"
    echo ""
    echo "选项:"
    echo "  -h, --help      - 显示帮助信息"
    echo "  -v, --verbose   - 详细输出"
    echo "  -q, --quiet     - 静默模式"
    echo "  --no-html       - 不生成HTML报告"
    echo "  --parallel      - 并行执行（实验性）"
    echo ""
    echo "示例:"
    echo "  $0 smoke                    # 运行冒烟测试"
    echo "  $0 preconditions -v        # 详细模式运行前置条件测试"
    echo "  $0 all --no-html           # 运行所有测试但不生成HTML报告"
}

# 检查依赖
check_dependencies() {
    echo -e "${YELLOW}检查依赖...${NC}"
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}错误: 未找到python3${NC}"
        exit 1
    fi
    
    # 检查pytest
    if ! python3 -c "import pytest" 2>/dev/null; then
        echo -e "${RED}错误: 未安装pytest${NC}"
        echo "请运行: pip install pytest pytest-html"
        exit 1
    fi
    
    # 检查测试目录
    if [ ! -d "tests" ]; then
        echo -e "${RED}错误: 未找到tests目录${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 依赖检查通过${NC}"
}

# 运行前置条件测试
run_preconditions_tests() {
    echo -e "${BLUE}运行前置条件测试${NC}"
    echo "========================"
    
    python3 -m pytest tests/test_preconditions.py tests/test_preconditions_integration.py \
        ${VERBOSE_FLAG} \
        --tb=short \
        ${HTML_FLAG} \
        || return 1
}

# 运行查询功能测试
run_query_tests() {
    echo -e "${BLUE}运行查询功能测试${NC}"
    echo "========================"
    
    python3 -m pytest tests/test_query_functions.py \
        ${VERBOSE_FLAG} \
        --tb=short \
        ${HTML_FLAG} \
        || return 1
}

# 运行期货案例测试
run_futures_tests() {
    echo -e "${BLUE}运行期货案例测试${NC}"
    echo "========================"
    
    python3 -m pytest tests/test_futures_cases.py \
        ${VERBOSE_FLAG} \
        --tb=short \
        ${HTML_FLAG} \
        || return 1
}

# 运行交易场景测试
run_scenarios_tests() {
    echo -e "${BLUE}运行交易场景测试${NC}"
    echo "=========================="
    
    python3 -m pytest tests/test_trading_scenarios.py \
        ${VERBOSE_FLAG} \
        --tb=short \
        ${HTML_FLAG} \
        || return 1
}

# 运行集成测试
run_integration_tests() {
    echo -e "${BLUE}运行集成测试${NC}"
    echo "=================="
    
    python3 -m pytest tests/ -m integration \
        ${VERBOSE_FLAG} \
        --tb=short \
        ${HTML_FLAG} \
        || return 1
}

# 运行冒烟测试
run_smoke_tests() {
    echo -e "${BLUE}运行冒烟测试${NC}"
    echo "=================="
    
    # 选择关键测试用例
    python3 -m pytest \
        tests/test_preconditions.py::TestPreconditions::test_precond_001_account_fund_sufficient \
        tests/test_preconditions.py::TestPreconditions::test_precond_005_system_normal \
        tests/test_query_functions.py::TestQueryFunctions::test_query_trading_account \
        ${VERBOSE_FLAG} \
        --tb=short \
        ${HTML_FLAG} \
        || return 1
}

# 运行所有测试
run_all_tests() {
    echo -e "${BLUE}运行所有测试${NC}"
    echo "=================="
    
    python3 -m pytest tests/ \
        ${VERBOSE_FLAG} \
        --tb=short \
        ${HTML_FLAG} \
        --junitxml=result/test_results/junit_report.xml \
        || return 1
}

# 并行运行测试（实验性）
run_parallel_tests() {
    echo -e "${BLUE}并行运行测试（实验性）${NC}"
    echo "=========================="
    
    if ! python3 -c "import pytest_xdist" 2>/dev/null; then
        echo -e "${YELLOW}警告: 未安装pytest-xdist，使用串行模式${NC}"
        run_all_tests
        return $?
    fi
    
    python3 -m pytest tests/ \
        -n auto \
        ${VERBOSE_FLAG} \
        --tb=short \
        ${HTML_FLAG} \
        || return 1
}

# 生成测试报告摘要
generate_summary() {
    if [ "$GENERATE_HTML" = "true" ]; then
        echo -e "${BLUE}生成测试报告摘要${NC}"
        echo "===================="
        
        SUMMARY_FILE="result/test_results/test_summary.html"
        
        cat > "$SUMMARY_FILE" << EOF
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>CTP期货交易测试摘要</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .report-list { margin-top: 20px; }
        .report-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .timestamp { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>CTP期货交易测试摘要</h1>
        <p class="timestamp">生成时间: $(date)</p>
    </div>
    <div class="report-list">
        <h2>测试报告文件</h2>
EOF

        # 添加报告文件链接
        for report in result/test_results/*.html; do
            if [ -f "$report" ] && [ "$(basename "$report")" != "test_summary.html" ]; then
                report_name=$(basename "$report" .html)
                echo "        <div class=\"report-item\"><a href=\"$(basename "$report")\">$report_name</a></div>" >> "$SUMMARY_FILE"
            fi
        done
        
        cat >> "$SUMMARY_FILE" << EOF
    </div>
</body>
</html>
EOF
        
        echo -e "${GREEN}✓ 测试摘要已生成: $SUMMARY_FILE${NC}"
    fi
}

# 清理测试环境
cleanup() {
    echo -e "${YELLOW}清理测试环境...${NC}"
    
    # 清理临时文件
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    
    # 清理pytest缓存
    rm -rf .pytest_cache 2>/dev/null || true
    
    echo -e "${GREEN}✓ 清理完成${NC}"
}

# 解析命令行参数
VERBOSE_FLAG=""
HTML_FLAG="--html=result/test_results/test_report.html --self-contained-html"
GENERATE_HTML="true"
PARALLEL="false"
TEST_TYPE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE_FLAG="-v"
            shift
            ;;
        -q|--quiet)
            VERBOSE_FLAG="-q"
            shift
            ;;
        --no-html)
            HTML_FLAG=""
            GENERATE_HTML="false"
            shift
            ;;
        --parallel)
            PARALLEL="true"
            shift
            ;;
        preconditions|query|futures|scenarios|integration|smoke|all)
            TEST_TYPE="$1"
            shift
            ;;
        *)
            echo -e "${RED}未知参数: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 如果没有指定测试类型，显示帮助
if [ -z "$TEST_TYPE" ]; then
    show_help
    exit 1
fi

# 主执行流程
main() {
    echo -e "${GREEN}开始执行CTP期货交易测试${NC}"
    echo "测试类型: $TEST_TYPE"
    echo "详细模式: ${VERBOSE_FLAG:-默认}"
    echo "HTML报告: ${GENERATE_HTML}"
    echo ""
    
    # 检查依赖
    check_dependencies
    
    # 记录开始时间
    START_TIME=$(date +%s)
    
    # 执行测试
    case $TEST_TYPE in
        preconditions)
            run_preconditions_tests
            ;;
        query)
            run_query_tests
            ;;
        futures)
            run_futures_tests
            ;;
        scenarios)
            run_scenarios_tests
            ;;
        integration)
            run_integration_tests
            ;;
        smoke)
            run_smoke_tests
            ;;
        all)
            if [ "$PARALLEL" = "true" ]; then
                run_parallel_tests
            else
                run_all_tests
            fi
            ;;
    esac
    
    TEST_RESULT=$?
    
    # 记录结束时间
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    # 生成报告摘要
    generate_summary
    
    # 显示结果
    echo ""
    echo "=================================="
    if [ $TEST_RESULT -eq 0 ]; then
        echo -e "${GREEN}🎉 测试执行成功!${NC}"
    else
        echo -e "${RED}❌ 测试执行失败!${NC}"
    fi
    echo "执行时间: ${DURATION}秒"
    echo "结果目录: result/test_results/"
    echo "=================================="
    
    return $TEST_RESULT
}

# 设置信号处理
trap cleanup EXIT
trap 'echo -e "\n${YELLOW}⚠️  测试被用户中断${NC}"; exit 130' INT TERM

# 执行主函数
main
exit $?