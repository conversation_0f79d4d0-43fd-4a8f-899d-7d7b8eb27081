
# 构建目录
BUILD_DIR := build
# 源文件目录
SRC_DIR := trader

#source file
#源文件，cpp文件，并将目标定义为同名.o文件
SOURCE  := $(filter-out $(SRC_DIR)/main.cpp, $(wildcard $(SRC_DIR)/*.c) $(wildcard $(SRC_DIR)/*.cpp))
OBJS    := $(patsubst $(SRC_DIR)/%.c,$(BUILD_DIR)/%.o,$(patsubst $(SRC_DIR)/%.cpp,$(BUILD_DIR)/%.o,$(SOURCE)))

#target you can change test to what you want
#目标文件名，输入任意你想要的执行文件
TARGET  := $(BUILD_DIR)/test

#compile and lib parameter
#编译参数
CC      := g++ -g  -std=c++11
LIBS    :=  -lthosttraderapi_se
LDFLAGS := -L./lib -Wl,-rpath,./lib
DEFINES :=
INCLUDE := -I. -I./lib/headers
CFLAGS  := -g -Wall -O0 -std=c++11 $(DEFINES) $(INCLUDE)
CXXFLAGS:= $(CFLAGS) -DHAVE_CONFIG_H

# 添加动态库目标
WEBSERVICE_LIB := $(BUILD_DIR)/libctp_webservice.so

# 更新目标
all : $(WEBSERVICE_LIB)

#i think you should do anything here
#下面的基本上不需要做任何改动
.PHONY : everything objs clean veryclean rebuild

everything : $(WEBSERVICE_LIB)

all : $(WEBSERVICE_LIB)

objs : $(OBJS)

rebuild: veryclean everything

clean :
	rm -rf $(BUILD_DIR)

# 创建构建目录
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

# 编译目标文件
$(BUILD_DIR)/%.o: $(SRC_DIR)/%.c | $(BUILD_DIR)
	$(CC) $(CFLAGS) -c $< -o $@

$(BUILD_DIR)/%.o: $(SRC_DIR)/%.cpp | $(BUILD_DIR)
	$(CC) $(CXXFLAGS) -c $< -o $@

# 移除TARGET编译规则
# $(TARGET) : $(OBJS) | $(BUILD_DIR)
# 	$(CC) $(CXXFLAGS) -o $@ $(OBJS) $(LDFLAGS) $(LIBS)

# 编译动态库
$(WEBSERVICE_LIB) : $(BUILD_DIR)/ctp_webservice.o $(BUILD_DIR)/ctp_direct_interface.o  | $(BUILD_DIR)
	$(CC) -shared -fPIC -o $@ $^ $(LDFLAGS) $(LIBS)

# 编译WebService对象文件
$(BUILD_DIR)/ctp_webservice.o: $(SRC_DIR)/ctp_webservice.cpp $(SRC_DIR)/ctp_config.h | $(BUILD_DIR)
	$(CC) $(CXXFLAGS) -fPIC -c $< -o $@

# 编译直接接口对象文件
$(BUILD_DIR)/ctp_direct_interface.o: $(SRC_DIR)/ctp_direct_interface.cpp $(SRC_DIR)/ctp_config.h | $(BUILD_DIR)
	$(CC) $(CXXFLAGS) -fPIC -c $< -o $@
