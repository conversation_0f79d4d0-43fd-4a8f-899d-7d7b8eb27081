#ifndef CTP_WEBSERVICE_H
#define CTP_WEBSERVICE_H

#include "ThostFtdcTraderApi.h"
#include "ThostFtdcUserApiDataType.h"
#include "ThostFtdcUserApiStruct.h"
#include "ctp_config.h"
#include <string>
#include <memory>
#include <vector>
#include <mutex>
#include <condition_variable>

// CTP WebService封装类
class CTPWebService : public CThostFtdcTraderSpi {
private:
    // 直接数据存储
    std::vector<CThostFtdcInvestorPositionField> m_position_results;
    std::vector<CThostFtdcInvestorPositionDetailField> m_position_detail_results;
    std::vector<CThostFtdcTradingAccountField> m_account_results;
    std::vector<CThostFtdcInstrumentField> m_instrument_results;
    std::vector<CThostFtdcTradeField> m_trade_results;
    std::vector<CThostFtdcOrderField> m_order_results;
    
    // 简化响应结构
    struct SimpleResponse {
        bool success;
        std::string error_msg;
    };
    SimpleResponse m_current_response;
    
    void setResponse(bool success, const std::string& error_msg = "");

public:
    // 直接回调函数类型定义 - 使用CTP原生结构
    typedef void (*OrderCallbackDirect)(const CThostFtdcOrderField* order_data);
    typedef void (*TradeCallbackDirect)(const CThostFtdcTradeField* trade_data);

public:
    CTPWebService();
    virtual ~CTPWebService();

    // 初始化连接
    bool initialize(const CTPConnectionConfig& config);
    
    // CTP回调接口实现
    void OnFrontConnected() override;
    void OnRspAuthenticate(CThostFtdcRspAuthenticateField *pRspAuthenticateField, 
                          CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspUserLogin(CThostFtdcRspUserLoginField *pRspUserLogin, 
                       CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspSettlementInfoConfirm(CThostFtdcSettlementInfoConfirmField *pSettlementInfoConfirm, 
                                   CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspQryTradingAccount(CThostFtdcTradingAccountField *pTradingAccount, 
                               CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspQryInvestorPosition(CThostFtdcInvestorPositionField *pInvestorPosition, 
                                 CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspQryTrade(CThostFtdcTradeField *pTrade, 
                      CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspQryOrder(CThostFtdcOrderField *pOrder, 
                      CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspQryInstrument(CThostFtdcInstrumentField *pInstrument,
                           CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspQryInstrumentMarginRate(CThostFtdcInstrumentMarginRateField *pInstrumentMarginRate,
                                     CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspQryInstrumentCommissionRate(CThostFtdcInstrumentCommissionRateField *pInstrumentCommissionRate,
                                         CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspQryDepthMarketData(CThostFtdcDepthMarketDataField *pDepthMarketData, 
                                CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspOrderInsert(CThostFtdcInputOrderField *pInputOrder,
                         CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRspOrderAction(CThostFtdcInputOrderActionField *pInputOrderAction,
                         CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    void OnRtnOrder(CThostFtdcOrderField *pOrder) override;
    void OnRtnTrade(CThostFtdcTradeField *pTrade) override;
    void OnFrontDisconnected(int nReason) override;
    void OnRspError(CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
    
    // 状态查询
    bool isConnected() const { return m_connected; }
    bool isLoggedIn() const { return m_logged_in; }
    
    // 直接回调设置
    void setOrderCallbackDirect(OrderCallbackDirect callback);
    void setTradeCallbackDirect(TradeCallbackDirect callback);
    
    // 辅助方法
    bool isErrorRspInfo(CThostFtdcRspInfoField *pRspInfo);
    void waitForResponse(int timeout_seconds = 5);
    void resetResponseState();
    
    // 直接数据访问接口
    const std::vector<CThostFtdcInvestorPositionField>& getPositionResults() const { return m_position_results; }
    const std::vector<CThostFtdcTradingAccountField>& getAccountResults() const { return m_account_results; }
    const std::vector<CThostFtdcInstrumentField>& getInstrumentResults() const { return m_instrument_results; }
    const std::vector<CThostFtdcTradeField>& getTradeResults() const { return m_trade_results; }
    const std::vector<CThostFtdcOrderField>& getOrderResults() const { return m_order_results; }
    const std::vector<CThostFtdcInvestorPositionDetailField>& getPositionDetailResults() const { return m_position_detail_results; }
    CThostFtdcTraderApi* getApi() { return m_api; }
    int getNextRequestId() { return ++m_request_id; }
    void clearResults();
    
    // 交易相关方法
    bool reqOrderInsert(const char* instrument_id, double price, int volume, 
                        char direction, char offset_flag, const char* exchange_id = "");
    bool reqOrderAction(const char* order_ref, const char* instrument_id, 
                        const char* exchange_id = "");
    
    // 查询相关方法
    bool reqQryInstrument(const char* instrument_id = "", const char* exchange_id = "");
    bool reqQryTradingAccount();
    bool reqQryInvestorPosition(const char* instrument_id = "");
    bool reqQryInvestorPositionDetail(const char* instrument_id = "");
    bool reqQryTrade();
    bool reqQryOrder();

    // 完整交易接口 - 请求+等待+获取结果
    bool queryInstrumentComplete(const char* instrument_id, const char* exchange_id, 
                                std::vector<CThostFtdcInstrumentField>& results);
    bool queryTradingAccountComplete(std::vector<CThostFtdcTradingAccountField>& results);
    bool queryInvestorPositionComplete(const char* instrument_id, 
                                      std::vector<CThostFtdcInvestorPositionField>& results);
    bool queryInvestorPositionDetailComplete(const char* instrument_id, 
                                           std::vector<CThostFtdcInvestorPositionDetailField>& results);
    bool queryTradeComplete(std::vector<CThostFtdcTradeField>& results);
    bool queryOrderComplete(std::vector<CThostFtdcOrderField>& results);

    // 完整交易操作接口 - 请求+等待+获取响应
    bool orderInsertComplete(const char* instrument_id, double price, int volume, 
                            char direction, char offset_flag, const char* exchange_id = "");
    bool orderActionComplete(const char* order_ref, const char* instrument_id, 
                            const char* exchange_id = "");

private:
    CThostFtdcTraderApi* m_api;
    CTPConnectionConfig m_config;
    int m_request_id;
    int m_order_ref;
    bool m_connected;
    bool m_logged_in;
    
    // 响应相关成员变量
    bool m_response_ready;
    std::mutex m_mutex;
    std::condition_variable m_cv;
    
    // 直接回调相关成员
    OrderCallbackDirect m_order_callback_direct;
    TradeCallbackDirect m_trade_callback_direct;
};

#endif // CTP_WEBSERVICE_H
