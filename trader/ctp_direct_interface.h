#ifndef CTP_DIRECT_INTERFACE_H
#define CTP_DIRECT_INTERFACE_H

#include "ThostFtdcUserApiStruct.h"
#include "ctp_config.h"

#ifdef __cplusplus
extern "C" {
#endif

// 确保结构体按1字节对齐，与Python端保持一致
#pragma pack(push, 1)
// 类型别名定义 - 与CTP原生结构体保持一致
typedef CThostFtdcTradingAccountField TradingAccountField;
typedef CThostFtdcInstrumentField InstrumentField;
typedef CThostFtdcInvestorPositionField InvestorPositionField;
typedef CThostFtdcInvestorPositionDetailField InvestorPositionDetailField;
typedef CThostFtdcTradeField TradeField;
typedef CThostFtdcOrderField OrderField;

#pragma pack(pop)

// 基础服务管理接口 - 直接使用统一配置结构
void* create_ctp_service();
bool initialize_service(void* service, const CTPConnectionConfig* config);
void destroy_ctp_service(void* service);
bool is_connected(void* service);
bool is_logged_in(void* service);

// 完整交易接口 - 请求+等待+获取结果的封装
bool query_instrument_complete(void* service, const char* instrument_id, const char* exchange_id, 
                              InstrumentField** results, int* count);
bool query_trading_account_complete(void* service, TradingAccountField** results, int* count);
bool query_investor_position_complete(void* service, const char* instrument_id, 
                                    InvestorPositionField** results, int* count);
bool query_investor_position_detail_complete(void* service, const char* instrument_id, 
                                            InvestorPositionDetailField** results, int* count);
bool query_trade_complete(void* service, TradeField** results, int* count);
bool query_order_complete(void* service, OrderField** results, int* count);

// 完整交易操作接口 - 请求+等待+获取结果的封装
bool order_insert_complete(void* service, const char* instrument_id, double price, int volume, 
                          char direction, char offset_flag, const char* exchange_id);
bool order_action_complete(void* service, const char* order_ref, const char* instrument_id, 
                          const char* exchange_id);

// 释放完整查询结果的内存
void free_complete_trading_account_data(TradingAccountField* data);
void free_complete_instrument_data(InstrumentField* data);
void free_complete_position_data(InvestorPositionField* data);
void free_complete_position_detail_data(InvestorPositionDetailField* data);
void free_complete_order_data(OrderField* data);
void free_complete_trade_data(TradeField* data);

#ifdef __cplusplus
}
#endif

#endif // CTP_DIRECT_INTERFACE_H
