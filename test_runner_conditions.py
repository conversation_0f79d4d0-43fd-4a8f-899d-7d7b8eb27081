#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试runner条件判断功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tests.test_condition_validation import TestConditionValidation
from tests.futures_test_executor import FuturesTestExecutor
from tests.futures_config_reader import FuturesConfigReader

def main():
    print("="*60)
    print("测试Runner条件判断功能")
    print("="*60)
    
    try:
        # 初始化
        config_reader = FuturesConfigReader()
        test_executor = FuturesTestExecutor(config_reader)
        test_validator = TestConditionValidation()
        
        print("\n1. 测试账户条件检查...")
        test_validator.test_account_condition_check(test_executor)
        
        print("\n2. 测试交易时间条件...")
        test_validator.test_trading_time_condition(test_executor)
        
        print("\n3. 测试系统状态条件...")
        test_validator.test_system_status_condition(test_executor)
        
        print("\n4. 测试验证规则...")
        test_validator.test_validation_rules(test_executor)
        
        print("\n5. 测试嵌套字段访问...")
        test_validator.test_nested_field_access(test_executor)
        
        print("\n6. 测试完整验证流程...")
        test_validator.test_complete_validation_flow(test_executor)
        
        print("\n" + "="*60)
        print("✓ 所有条件判断功能测试通过")
        print("Runner条件判断功能正常工作")
        print("="*60)
        
    except Exception as e:
        print(f"\n✗ 条件判断功能测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()