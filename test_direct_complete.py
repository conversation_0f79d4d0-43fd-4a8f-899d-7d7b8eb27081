#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的直接数据传递功能
包括查询、下单和回调的直接接口
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

class TestResultLogger:
    """测试结果记录器"""
    
    def __init__(self):
        self.results = {
            'test_name': '直接接口完整功能测试',
            'start_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'test_steps': [],
            'summary': {}
        }
        
        # 确保结果目录存在
        os.makedirs('./result', exist_ok=True)
    
    def log_step(self, step_name, params, result, success=True, error_msg=""):
        """记录测试步骤"""
        step_result = {
            'step_name': step_name,
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'input_params': params,
            'result': result,
            'success': success,
            'error_message': error_msg
        }
        self.results['test_steps'].append(step_result)
        print(f"✓ 已记录步骤: {step_name}")
    
def print_trading_params(operation_name, **params):
    """以表格形式打印交易输入参数"""
    print(f"\n📋 {operation_name} - 输入参数")
    print("="*60)
    print(f"{'参数名':<20} {'参数值':<30}")
    print("-"*60)
    for key, value in params.items():
        print(f"{key:<20} {str(value):<30}")
    print("="*60)

def print_trading_result(operation_name, result):
    """以表格形式打印交易返回结果"""
    print(f"\n📊 {operation_name} - 返回结果")
    print("="*80)
    
    if isinstance(result, dict):
        if result.get('success'):
            print("✓ 操作状态: 成功")
        else:
            print("✗ 操作状态: 失败")
            
        print(f"{'字段名':<25} {'字段值':<50}")
        print("-"*80)
        
        # 递归打印字典内容
        def print_dict_items(data, prefix=""):
            for key, value in data.items():
                if isinstance(value, dict):
                    print(f"{(prefix + key):<25} {'[字典对象]':<50}")
                    print_dict_items(value, prefix + "  ")
                elif isinstance(value, list):
                    print(f"{(prefix + key):<25} {'[列表对象, 长度: ' + str(len(value)) + ']':<50}")
                    for i, item in enumerate(value[:3]):  # 只显示前3个元素
                        if isinstance(item, dict):
                            print(f"{(prefix + f'  [{i}]'):<25} {'[字典对象]':<50}")
                        else:
                            print(f"{(prefix + f'  [{i}]'):<25} {str(item):<50}")
                    if len(value) > 3:
                        print(f"{(prefix + '  ...'):<25} {'[还有' + str(len(value)-3) + '个元素]':<50}")
                else:
                    print(f"{(prefix + key):<25} {str(value):<50}")
        
        print_dict_items(result)
    else:
        print(f"返回值类型: {type(result)}")
        print(f"返回值: {result}")
    
    print("="*80)

def test_direct_complete():
    """测试完整的直接数据传递功能"""
    logger = TestResultLogger()
    
    print("开始测试完整的直接数据传递功能...")
    print("="*80)
    
    try:
        # 检查库文件
        lib_path = "./build/libctp_webservice.so"
        logger.log_step(
            "检查库文件", 
            {"lib_path": lib_path}, 
            {"exists": os.path.exists(lib_path)},
            success=os.path.exists(lib_path)
        )
        
        if not os.path.exists(lib_path):
            logger.save_results()
            return
        
        # 导入和初始化
        from trader.ctp_direct_wrapper import CTPDirectWrapper
        service = CTPDirectWrapper()
        
        init_result = service.initialize()
        logger.log_step(
            "CTP服务初始化",
            {},
            {"success": init_result},
            success=init_result
        )
        
        if not init_result:
            logger.save_results()
            return
        
        # 等待登录
        login_success = False
        for i in range(10):
            if service.is_logged_in():
                login_success = True
                break
            time.sleep(1)
        
        logger.log_step(
            "等待登录完成",
            {"timeout": 10},
            {"login_success": login_success},
            success=login_success
        )
        
        if not login_success:
            return
        
        print(service.connection_config)

        # 资金账户查询 - 使用wrapper的接口
        print_trading_params("资金账户查询")
        
        initial_account = service.query_trading_account()
        print_trading_result("资金账户查询", initial_account)
        
        logger.log_step(
            "资金账户查询",
            {},
            initial_account,
            success=initial_account.get('success', False)
        )
        
        # 合约查询
        instrument_params = {'instrument_id': "IF2512"}
        print_trading_params("合约查询", **instrument_params)
        
        instrument_result = service.query_instrument(**instrument_params)
        print_trading_result("合约查询", instrument_result)
        
        logger.log_step(
            "合约查询",
            instrument_params,
            instrument_result,
            success=instrument_result.get('success', False)
        )
        
        # 持仓查询 - 使用wrapper的接口
        print_trading_params("持仓查询")
        
        after_buy_position = service.query_investor_position()
        print_trading_result("持仓查询", after_buy_position)
        
        logger.log_step(
            "持仓查询",
            {},
            after_buy_position,
            success=after_buy_position.get('success', False)
        )
        
        # 持仓明细查询
        position_detail_params = {'instrument_id': "IF2512"}
        print_trading_params("持仓明细查询", **position_detail_params)
        
        position_detail_result = service.query_investor_position_detail(**position_detail_params)
        print_trading_result("持仓明细查询", position_detail_result)
        
        logger.log_step(
            "持仓明细查询",
            position_detail_params,
            position_detail_result,
            success=position_detail_result.get('success', False)
        )
        
        # 报单查询
        print_trading_params("报单查询")
        
        order_query_result = service.query_order()
        print_trading_result("报单查询", order_query_result)
        
        logger.log_step(
            "报单查询",
            {},
            order_query_result,
            success=order_query_result.get('success', False)
        )
        
        # 成交查询
        print_trading_params("成交查询")
        
        trade_query_result = service.query_trade()
        print_trading_result("成交查询", trade_query_result)
        
        logger.log_step(
            "成交查询",
            {},
            trade_query_result,
            success=trade_query_result.get('success', False)
        )
        
        # 测试直接下单功能
        print("\n" + "="*80)
        print("测试直接下单功能")

        # 打印下单输入参数
        order_params = {
            'instrument_id': "IF2512",
            'direction': "买",
            'offset_flag': "开仓",
            'price': 4089.0,
            'volume': 1,
            'exchange_id': "CFFEX"
        }
        print_trading_params("直接下单", **order_params)

        order_result = service.order_insert(**order_params)

        # 打印下单返回结果
        print_trading_result("直接下单", order_result)

        logger.log_step(
            "直接下单",
            order_params,
            order_result,
            success=order_result.get('success', False)
        )
        
        # 等待一段时间让订单处理
        time.sleep(2)
        
        # 再次查询报单状态
        print_trading_params("下单后报单查询")
        
        after_order_query = service.query_order()
        print_trading_result("下单后报单查询", after_order_query)
        
        logger.log_step(
            "下单后报单查询",
            {},
            after_order_query,
            success=after_order_query.get('success', False)
        )
        
        # 如果有报单，尝试撤单操作
        if (after_order_query.get('success') and 
            after_order_query.get('data') and 
            len(after_order_query['data']) > 0):
            
            # 获取最新的报单信息
            latest_order = after_order_query['data'][-1]
            order_ref = latest_order.get('order_ref', '')
            instrument_id = latest_order.get('instrument_id', '')
            exchange_id = latest_order.get('exchange_id', '')
            order_status = latest_order.get('order_status', '')
            
            # 只对未成交或部分成交的订单进行撤单
            if order_ref and order_status in ['未成交', '部分成交', '0', '1']:
                cancel_params = {
                    'order_ref': order_ref,
                    'instrument_id': instrument_id,
                    'exchange_id': exchange_id
                }
                print_trading_params("撤单操作", **cancel_params)
                
                cancel_result = service.order_action(**cancel_params)
                print_trading_result("撤单操作", cancel_result)
                
                logger.log_step(
                    "撤单操作",
                    cancel_params,
                    cancel_result,
                    success=cancel_result.get('success', False)
                )
                
                # 等待撤单处理
                time.sleep(2)
                
                # 撤单后再次查询报单状态
                print_trading_params("撤单后报单查询")
                
                final_order_query = service.query_order()
                print_trading_result("撤单后报单查询", final_order_query)
                
                logger.log_step(
                    "撤单后报单查询",
                    {},
                    final_order_query,
                    success=final_order_query.get('success', False)
                )
            else:
                print(f"\n⚠️  订单状态为 '{order_status}'，无需撤单")
                logger.log_step(
                    "撤单检查",
                    {"order_status": order_status},
                    {"message": "订单状态不需要撤单"},
                    success=True
                )
        else:
            print("\n⚠️  未找到可撤销的报单")
            logger.log_step(
                "撤单检查",
                {},
                {"message": "未找到可撤销的报单"},
                success=True
            )
        
        # 最终状态查询
        print("\n" + "="*80)
        print("最终状态查询")
        
        # 最终资金查询
        print_trading_params("最终资金查询")
        
        final_account = service.query_trading_account()
        print_trading_result("最终资金查询", final_account)
        
        logger.log_step(
            "最终资金查询",
            {},
            final_account,
            success=final_account.get('success', False)
        )
        
        # 最终持仓查询
        print_trading_params("最终持仓查询")
        
        final_position = service.query_investor_position()
        print_trading_result("最终持仓查询", final_position)
        
        logger.log_step(
            "最终持仓查询",
            {},
            final_position,
            success=final_position.get('success', False)
        )
        
        # 最终成交查询
        print_trading_params("最终成交查询")
        
        final_trade = service.query_trade()
        print_trading_result("最终成交查询", final_trade)
        
        logger.log_step(
            "最终成交查询",
            {},
            final_trade,
            success=final_trade.get('success', False)
        )
    except Exception as e:
        logger.log_step(
            "测试执行",
            {},
            {"error": str(e)},
            success=False,
            error_msg=str(e)
        )
        import traceback
        traceback.print_exc()
    
if __name__ == "__main__":
    test_direct_complete()
