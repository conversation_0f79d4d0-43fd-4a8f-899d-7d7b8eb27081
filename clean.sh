#!/bin/bash

# CTP模拟器项目清理脚本
# 清理所有编译文件和Python缓存文件

echo "开始清理CTP模拟器项目..."

# 清理编译文件
echo "清理编译文件..."
if [ -d "build" ]; then
    echo "  删除 build/ 目录"
    rm -rf build/
fi

# 清理可执行文件
echo "清理可执行文件..."
if [ -f "test_encoding" ]; then
    echo "  删除 test_encoding"
    rm -f test_encoding
fi

# 清理临时文件
echo "清理临时文件..."
find . -name "*.o" -type f -delete
find . -name "*.a" -type f -delete
find . -name "*.dylib" -type f -delete
find . -name "*.dll" -type f -delete
find . -name "*.exe" -type f -delete

# 清理Python缓存文件
echo "清理Python缓存文件..."
find . -name "*.pyc" -type f -delete
find . -name "*.pyo" -type f -delete
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null
find . -name ".pytest_cache" -type d -exec rm -rf {} + 2>/dev/null
# 强制清理所有Python缓存
python3 -Bc "import compileall; compileall.compile_dir('.', force=True, quiet=1)" 2>/dev/null || true
find . -path "*/__pycache__/*" -delete 2>/dev/null || true

# 清理Python egg文件
echo "清理Python egg文件..."
find . -name "*.egg-info" -type d -exec rm -rf {} + 2>/dev/null

# 清理IDE和编辑器临时文件
echo "清理IDE和编辑器临时文件..."
find . -name ".vscode" -type d -exec rm -rf {} + 2>/dev/null
find . -name ".idea" -type d -exec rm -rf {} + 2>/dev/null
find . -name "*.swp" -type f -delete
find . -name "*.swo" -type f -delete
find . -name "*~" -type f -delete
find . -name ".DS_Store" -type f -delete

# 清理日志文件
echo "清理日志文件..."
find . -name "*.log" -type f -delete
find . -name "*.out" -type f -delete

# 清理CTP相关的临时文件
echo "清理CTP相关临时文件..."
find . -name "TradingDay.txt" -type f -delete
find . -name "*.con" -type f -delete
find . -name "*.flow" -type f -delete

# 清理测试相关文件
echo "清理测试相关文件..."
find . -name ".coverage" -type f -delete
find . -name "coverage.xml" -type f -delete
find . -name "htmlcov" -type d -exec rm -rf {} + 2>/dev/null

# 清理数据目录中的临时文件（保留目录结构）
echo "清理数据目录临时文件..."
if [ -d "data" ]; then
    find data/ -name "*.tmp" -type f -delete 2>/dev/null
    find data/ -name "*.temp" -type f -delete 2>/dev/null
fi

# 清理输出目录中的临时文件（保留目录结构）
echo "清理输出目录临时文件..."
if [ -d "output" ]; then
    find output/ -name "*.tmp" -type f -delete 2>/dev/null
    find output/ -name "*.temp" -type f -delete 2>/dev/null
fi

# 显示清理结果
echo ""
echo "清理完成！"
echo "已清理的内容："
echo "  ✓ 编译文件 (build/, *.o, *.a 等)"
echo "  ✓ Python缓存文件 (*.pyc, __pycache__ 等)"
echo "  ✓ IDE临时文件 (.vscode, .idea 等)"
echo "  ✓ 日志文件 (*.log, *.out 等)"
echo "  ✓ CTP临时文件 (TradingDay.txt, *.con 等)"
echo "  ✓ 测试相关文件 (.coverage, htmlcov 等)"
echo ""
echo "项目已清理完毕，可以重新编译。"
