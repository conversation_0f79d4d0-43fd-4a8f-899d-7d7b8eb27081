#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行直接接口测试并生成报告
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tests.automated_test_runner import AutomatedTestRunner

def main():
    # 修改配置为执行直接接口测试场景
    runner = AutomatedTestRunner()
    runner.exec_config.mode = "scenario"
    runner.exec_config.target = "direct_complete_test"
    runner.run()

if __name__ == "__main__":
    main()