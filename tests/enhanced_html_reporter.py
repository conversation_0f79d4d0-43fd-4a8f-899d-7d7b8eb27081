#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的HTML报告生成器
生成包含详细环境配置、步骤设计、交易配置、报文和执行结果的HTML报告
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from execution_config_reader import ExecutionConfigReader, ReportConfig

class EnhancedHTMLReporter:
    """增强的HTML报告生成器"""
    
    def __init__(self, config_reader: ExecutionConfigReader):
        self.config_reader = config_reader
        self.report_config = config_reader.get_report_config()
        self.env_config = config_reader.get_environment_config()
        self.test_data_config = config_reader.get_test_data_config()
    
    def generate_scenario_report(self, scenario_results: List[Dict[str, Any]], output_path: str = None) -> str:
        """生成场景测试报告"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = self.report_config.output.get('filename_template', 'futures_test_report_{timestamp}.html')
            filename = filename.format(timestamp=timestamp)
            output_path = os.path.join(self.env_config.directories.get('result', './result'), filename)
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 生成HTML内容
        html_content = self._generate_html_content(scenario_results)
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return output_path
    
    def _generate_html_content(self, scenario_results: List[Dict[str, Any]]) -> str:
        """生成HTML内容"""
        html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.report_config.title}</title>
    <style>
        {self._get_css_styles()}
    </style>
</head>
<body>
    <div class="container">
        <header class="report-header">
            <h1>{self.report_config.title}</h1>
            <div class="report-meta">
                <span>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</span>
                <span>场景数量: {len(scenario_results)}</span>
            </div>
        </header>
        
        {self._generate_summary_section(scenario_results)}
        
        {self._generate_environment_section() if self.report_config.include_environment else ''}
        
        {self._generate_scenarios_section(scenario_results)}
    </div>
    
    <script>
        {self._get_javascript()}
    </script>
</body>
</html>"""
        return html
    
    def _generate_summary_section(self, scenario_results: List[Dict[str, Any]]) -> str:
        """生成摘要部分"""
        total_scenarios = len(scenario_results)
        total_cases = sum(len(r.get('test_case_results', [])) for r in scenario_results)
        total_steps = sum(r.get('summary', {}).get('total_steps', 0) for r in scenario_results)
        success_steps = sum(r.get('summary', {}).get('success_steps', 0) for r in scenario_results)
        
        success_rate = (success_steps / total_steps * 100) if total_steps > 0 else 0
        
        return f"""
        <section class="summary-section">
            <h2>执行摘要</h2>
            <div class="summary-cards">
                <div class="summary-card">
                    <div class="card-title">测试场景</div>
                    <div class="card-value">{total_scenarios}</div>
                </div>
                <div class="summary-card">
                    <div class="card-title">测试案例</div>
                    <div class="card-value">{total_cases}</div>
                </div>
                <div class="summary-card">
                    <div class="card-title">测试步骤</div>
                    <div class="card-value">{total_steps}</div>
                </div>
                <div class="summary-card success">
                    <div class="card-title">成功率</div>
                    <div class="card-value">{success_rate:.1f}%</div>
                </div>
            </div>
        </section>
        """
    
    def _generate_environment_section(self) -> str:
        """生成环境配置部分"""
        return f"""
        <section class="environment-section">
            <h2>环境配置</h2>
            <div class="config-grid">
                <div class="config-group">
                    <h3>CTP连接配置</h3>
                    <table class="config-table">
                        <tr><td>交易前置机</td><td>{self.env_config.ctp_config.get('trade_front', 'N/A')}</td></tr>
                        <tr><td>行情前置机</td><td>{self.env_config.ctp_config.get('md_front', 'N/A')}</td></tr>
                        <tr><td>经纪商ID</td><td>{self.env_config.ctp_config.get('broker_id', 'N/A')}</td></tr>
                        <tr><td>用户ID</td><td>{self.env_config.ctp_config.get('user_id', 'N/A')}</td></tr>
                        <tr><td>投资者ID</td><td>{self.env_config.ctp_config.get('investor_id', 'N/A')}</td></tr>
                    </table>
                </div>
                <div class="config-group">
                    <h3>系统配置</h3>
                    <table class="config-table">
                        <tr><td>超时时间</td><td>{self.env_config.system.get('timeout', 'N/A')}秒</td></tr>
                        <tr><td>重试次数</td><td>{self.env_config.system.get('retry_count', 'N/A')}</td></tr>
                        <tr><td>日志级别</td><td>{self.env_config.system.get('log_level', 'N/A')}</td></tr>
                        <tr><td>调试模式</td><td>{'是' if self.env_config.system.get('enable_debug') else '否'}</td></tr>
                    </table>
                </div>
                <div class="config-group">
                    <h3>测试数据配置</h3>
                    <table class="config-table">
                        <tr><td>初始资金</td><td>{self.test_data_config.account.get('initial_fund', 0):,.2f}元</td></tr>
                        <tr><td>可用资金</td><td>{self.test_data_config.account.get('available_fund', 0):,.2f}元</td></tr>
                        <tr><td>已用保证金</td><td>{self.test_data_config.account.get('margin_used', 0):,.2f}元</td></tr>
                        <tr><td>配置合约数</td><td>{len(self.test_data_config.instruments)}个</td></tr>
                    </table>
                </div>
            </div>
            
            <div class="instruments-section">
                <h3>合约配置详情</h3>
                <table class="instruments-table">
                    <thead>
                        <tr>
                            <th>合约代码</th>
                            <th>合约名称</th>
                            <th>交易所</th>
                            <th>合约乘数</th>
                            <th>保证金率</th>
                            <th>最小变动价位</th>
                            <th>当前价格</th>
                        </tr>
                    </thead>
                    <tbody>
                        {self._generate_instruments_rows()}
                    </tbody>
                </table>
            </div>
        </section>
        """
    
    def _generate_instruments_rows(self) -> str:
        """生成合约配置行"""
        rows = []
        for instrument_id, config in self.test_data_config.instruments.items():
            row = f"""
            <tr>
                <td>{instrument_id}</td>
                <td>{config.get('name', 'N/A')}</td>
                <td>{config.get('exchange', 'N/A')}</td>
                <td>{config.get('multiplier', 'N/A')}</td>
                <td>{config.get('margin_rate', 'N/A')}</td>
                <td>{config.get('price_tick', 'N/A')}</td>
                <td>{config.get('current_price', 'N/A')}</td>
            </tr>
            """
            rows.append(row)
        return ''.join(rows)
    
    def _generate_scenarios_section(self, scenario_results: List[Dict[str, Any]]) -> str:
        """生成场景详情部分"""
        scenarios_html = []
        
        for i, scenario_result in enumerate(scenario_results, 1):
            scenario_info = scenario_result.get('scenario_info', {})
            summary = scenario_result.get('summary', {})
            
            scenario_html = f"""
            <section class="scenario-section">
                <div class="scenario-header">
                    <h2>场景 {i}: {scenario_info.get('scenario_name', 'N/A')}</h2>
                    <div class="scenario-meta">
                        <span class="scenario-id">ID: {scenario_info.get('scenario_id', 'N/A')}</span>
                        <span class="scenario-category">分类: {scenario_info.get('category', 'N/A')}</span>
                        <span class="scenario-duration">耗时: {scenario_result.get('duration', 0):.2f}秒</span>
                        <span class="scenario-success-rate">成功率: {summary.get('overall_success_rate', 0):.1f}%</span>
                    </div>
                </div>
                
                <div class="scenario-description">
                    <p>{scenario_info.get('description', 'N/A')}</p>
                </div>
                
                {self._generate_test_cases_section(scenario_result.get('test_case_results', []))}
            </section>
            """
            scenarios_html.append(scenario_html)
        
        return ''.join(scenarios_html)
    
    def _generate_test_cases_section(self, test_case_results: List[Dict[str, Any]]) -> str:
        """生成测试案例部分"""
        cases_html = []
        
        for j, case_result in enumerate(test_case_results, 1):
            case_info = case_result.get('test_case_info', {})
            summary = case_result.get('summary', {})
            
            case_html = f"""
            <div class="test-case">
                <div class="case-header">
                    <h3>案例 {j}: {case_info.get('test_name', 'N/A')}</h3>
                    <div class="case-meta">
                        <span class="case-id">ID: {case_info.get('test_id', 'N/A')}</span>
                        <span class="case-category">分类: {case_info.get('category', 'N/A')}</span>
                        <span class="case-priority">优先级: {case_info.get('priority', 'N/A')}</span>
                        <span class="case-duration">耗时: {case_result.get('duration', 0):.2f}秒</span>
                        <span class="case-success-rate">成功率: {summary.get('success_rate', 0):.1f}%</span>
                    </div>
                </div>
                
                <div class="case-description">
                    <p>{case_info.get('description', 'N/A')}</p>
                </div>
                
                {self._generate_steps_section(case_result.get('step_results', []))}
            </div>
            """
            cases_html.append(case_html)
        
        return ''.join(cases_html)
    
    def _generate_steps_section(self, step_results: List[Dict[str, Any]]) -> str:
        """生成测试步骤部分"""
        steps_html = []
        
        for k, step_result in enumerate(step_results, 1):
            status_class = step_result.get('status', '失败').lower()
            if status_class == '成功':
                status_class = 'success'
            elif status_class == '失败':
                status_class = 'failed'
            else:
                status_class = 'skipped'
            
            step_html = f"""
            <div class="test-step {status_class}">
                <div class="step-header">
                    <h4>步骤 {k}: {step_result.get('step_name', 'N/A')}</h4>
                    <div class="step-meta">
                        <span class="step-id">ID: {step_result.get('step_id', 'N/A')}</span>
                        <span class="step-status status-{status_class}">{step_result.get('status', 'N/A')}</span>
                        <span class="step-duration">耗时: {step_result.get('duration', 0):.3f}秒</span>
                    </div>
                </div>
                
                {self._generate_step_details(step_result)}
            </div>
            """
            steps_html.append(step_html)
        
        return f'<div class="steps-container">{"".join(steps_html)}</div>'

    def _generate_step_details(self, step_result: Dict[str, Any]) -> str:
        """生成步骤详情"""
        actual_result = step_result.get('actual_result', {})
        validation_results = step_result.get('validation_results', [])

        details_html = []

        # 步骤设计详情
        if self.report_config.include_step_details:
            details_html.append(f"""
            <div class="step-design">
                <h5>步骤设计</h5>
                <div class="design-info">
                    <p><strong>步骤描述:</strong> {step_result.get('step_description', 'N/A')}</p>
                    <p><strong>步骤类型:</strong> {step_result.get('step_type', 'N/A')}</p>
                    <p><strong>执行时间:</strong> {step_result.get('start_time', 'N/A')} - {step_result.get('end_time', 'N/A')}</p>
                </div>
            </div>
            """)

        # 交易配置
        if self.report_config.include_trading_config and 'order_params' in actual_result:
            trading_config = actual_result['order_params']
            details_html.append(f"""
            <div class="trading-config">
                <h5>交易配置</h5>
                <table class="config-table">
                    {self._generate_config_rows(trading_config)}
                </table>
            </div>
            """)

        # 报文和回文
        if self.report_config.include_messages:
            details_html.append(f"""
            <div class="messages-section">
                <h5>报文和回文</h5>
                <div class="message-tabs">
                    <button class="tab-button active" onclick="showTab(event, 'request-{step_result.get('step_id', '')}')">请求报文</button>
                    <button class="tab-button" onclick="showTab(event, 'response-{step_result.get('step_id', '')}')">响应报文</button>
                </div>
                <div id="request-{step_result.get('step_id', '')}" class="tab-content active">
                    <pre class="message-content">{self._format_request_message(step_result)}</pre>
                </div>
                <div id="response-{step_result.get('step_id', '')}" class="tab-content">
                    <pre class="message-content">{self._format_response_message(actual_result)}</pre>
                </div>
            </div>
            """)

        # 执行结果检查
        if self.report_config.include_execution_check:
            details_html.append(f"""
            <div class="execution-check">
                <h5>执行结果检查</h5>
                <div class="result-summary">
                    <p><strong>执行状态:</strong> <span class="status-{step_result.get('status', '').lower()}">{step_result.get('status', 'N/A')}</span></p>
                    {f'<p><strong>错误信息:</strong> {step_result.get("error_message", "")}</p>' if step_result.get('error_message') else ''}
                </div>

                <div class="actual-results">
                    <h6>实际结果</h6>
                    <table class="result-table">
                        {self._generate_result_rows(actual_result)}
                    </table>
                </div>

                {self._generate_validation_results(validation_results)}
            </div>
            """)

        return ''.join(details_html)

    def _generate_config_rows(self, config: Dict[str, Any]) -> str:
        """生成配置表格行"""
        rows = []
        for key, value in config.items():
            rows.append(f"<tr><td>{key}</td><td>{value}</td></tr>")
        return ''.join(rows)

    def _generate_result_rows(self, result: Dict[str, Any]) -> str:
        """生成结果表格行"""
        rows = []
        for key, value in result.items():
            if key != 'raw_output':  # 排除原始输出
                rows.append(f"<tr><td>{key}</td><td>{value}</td></tr>")
        return ''.join(rows)

    def _generate_validation_results(self, validation_results: List[Dict[str, Any]]) -> str:
        """生成验证结果"""
        if not validation_results:
            return ""

        rows = []
        for validation in validation_results:
            status_class = validation.get('status', '失败').lower()
            if status_class == '通过':
                status_class = 'success'
            elif status_class == '失败':
                status_class = 'failed'
            else:
                status_class = 'skipped'

            rows.append(f"""
            <tr class="validation-{status_class}">
                <td>{validation.get('rule_name', 'N/A')}</td>
                <td>{validation.get('field_name', 'N/A')}</td>
                <td>{validation.get('expected', 'N/A')}</td>
                <td>{validation.get('actual', 'N/A')}</td>
                <td><span class="status-{status_class}">{validation.get('status', 'N/A')}</span></td>
                <td>{validation.get('message', '')}</td>
            </tr>
            """)

        return f"""
        <div class="validation-results">
            <h6>验证结果</h6>
            <table class="validation-table">
                <thead>
                    <tr>
                        <th>验证规则</th>
                        <th>字段名</th>
                        <th>期望值</th>
                        <th>实际值</th>
                        <th>状态</th>
                        <th>消息</th>
                    </tr>
                </thead>
                <tbody>
                    {''.join(rows)}
                </tbody>
            </table>
        </div>
        """

    def _format_request_message(self, step_result: Dict[str, Any]) -> str:
        """格式化请求报文"""
        # 模拟CTP请求报文格式
        step_type = step_result.get('step_type', '')
        step_id = step_result.get('step_id', '')

        if step_type == "查询账户":
            return f"""CTP请求报文 - 查询账户资金
RequestID: {step_id}
Function: ReqQryTradingAccount
Fields:
  BrokerID: 9999
  InvestorID: test_investor
  CurrencyID: CNY
Timestamp: {step_result.get('start_time', 'N/A')}"""

        elif step_type == "查询合约":
            return f"""CTP请求报文 - 查询合约信息
RequestID: {step_id}
Function: ReqQryInstrument
Fields:
  InstrumentID: IF2501
  ExchangeID: CFFEX
Timestamp: {step_result.get('start_time', 'N/A')}"""

        elif step_type == "下单交易":
            actual_result = step_result.get('actual_result', {})
            order_params = actual_result.get('order_params', {})
            return f"""CTP请求报文 - 报单录入
RequestID: {step_id}
Function: ReqOrderInsert
Fields:
  BrokerID: 9999
  InvestorID: test_investor
  InstrumentID: {order_params.get('instrument_id', 'N/A')}
  OrderRef: {step_id}
  Direction: {order_params.get('direction', 'N/A')}
  CombOffsetFlag: {order_params.get('offset_flag', 'N/A')}
  CombHedgeFlag: {order_params.get('hedge_flag', 'N/A')}
  LimitPrice: {order_params.get('limit_price', 'N/A')}
  VolumeTotalOriginal: {order_params.get('volume', 'N/A')}
  TimeCondition: GTC
  VolumeCondition: AV
  OrderPriceType: {order_params.get('price_type', 'N/A')}
Timestamp: {step_result.get('start_time', 'N/A')}"""

        else:
            return f"""CTP请求报文 - {step_type}
RequestID: {step_id}
Function: {step_type}
Timestamp: {step_result.get('start_time', 'N/A')}"""

    def _format_response_message(self, actual_result: Dict[str, Any]) -> str:
        """格式化响应报文"""
        raw_output = actual_result.get('raw_output', '')
        status = actual_result.get('status', 'failed')

        if raw_output:
            return f"""CTP响应报文
Status: {status}
Raw Output:
{raw_output}
Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        else:
            return f"""CTP响应报文
Status: {status}
Result: {json.dumps(actual_result, ensure_ascii=False, indent=2)}
Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

    def _get_css_styles(self) -> str:
        """获取CSS样式"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .report-header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .report-meta {
            display: flex;
            justify-content: center;
            gap: 30px;
            font-size: 1.1em;
        }

        .summary-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .summary-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #007bff;
        }

        .summary-card.success {
            border-left-color: #28a745;
        }

        .card-title {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 10px;
        }

        .card-value {
            font-size: 2em;
            font-weight: bold;
            color: #333;
        }

        .environment-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .config-group h3 {
            color: #495057;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .config-table, .instruments-table, .result-table, .validation-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .config-table th, .config-table td,
        .instruments-table th, .instruments-table td,
        .result-table th, .result-table td,
        .validation-table th, .validation-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .config-table th, .instruments-table th, .result-table th, .validation-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .instruments-section {
            margin-top: 30px;
        }

        .scenario-section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .scenario-header {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px 30px;
        }

        .scenario-header h2 {
            margin-bottom: 10px;
        }

        .scenario-meta, .case-meta, .step-meta {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .scenario-description, .case-description {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .test-case {
            border-bottom: 1px solid #dee2e6;
        }

        .case-header {
            background: #e3f2fd;
            padding: 15px 30px;
        }

        .case-header h3 {
            color: #1976d2;
            margin-bottom: 8px;
        }

        .steps-container {
            padding: 0 30px 20px;
        }

        .test-step {
            margin: 20px 0;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }

        .test-step.success {
            border-left: 4px solid #28a745;
        }

        .test-step.failed {
            border-left: 4px solid #dc3545;
        }

        .test-step.skipped {
            border-left: 4px solid #ffc107;
        }

        .step-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .step-header h4 {
            color: #495057;
            margin-bottom: 8px;
        }

        .step-design, .trading-config, .messages-section, .execution-check {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f3f4;
        }

        .step-design:last-child, .trading-config:last-child,
        .messages-section:last-child, .execution-check:last-child {
            border-bottom: none;
        }

        .step-design h5, .trading-config h5, .messages-section h5, .execution-check h5 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .design-info p {
            margin-bottom: 5px;
        }

        .message-tabs {
            display: flex;
            margin-bottom: 10px;
        }

        .tab-button {
            background: #e9ecef;
            border: none;
            padding: 8px 16px;
            cursor: pointer;
            border-radius: 4px 4px 0 0;
            margin-right: 2px;
        }

        .tab-button.active {
            background: #007bff;
            color: white;
        }

        .tab-content {
            display: none;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 0 4px 4px 4px;
        }

        .tab-content.active {
            display: block;
        }

        .message-content {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .status-success {
            color: #28a745;
            font-weight: bold;
        }

        .status-failed {
            color: #dc3545;
            font-weight: bold;
        }

        .status-skipped {
            color: #ffc107;
            font-weight: bold;
        }

        .validation-success {
            background-color: #d4edda;
        }

        .validation-failed {
            background-color: #f8d7da;
        }

        .validation-skipped {
            background-color: #fff3cd;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .report-meta, .scenario-meta, .case-meta, .step-meta {
                flex-direction: column;
                gap: 10px;
            }

            .config-grid {
                grid-template-columns: 1fr;
            }

            .summary-cards {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        """

    def _get_javascript(self) -> str:
        """获取JavaScript代码"""
        return """
        function showTab(evt, tabName) {
            var i, tabcontent, tablinks;

            // 获取同一组的所有tab内容
            var container = evt.target.closest('.messages-section');
            tabcontent = container.getElementsByClassName('tab-content');
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].classList.remove('active');
            }

            // 获取同一组的所有tab按钮
            tablinks = container.getElementsByClassName('tab-button');
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].classList.remove('active');
            }

            // 显示选中的tab内容和按钮
            document.getElementById(tabName).classList.add('active');
            evt.target.classList.add('active');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('CTP期货交易自动化测试报告已加载');
        });
        """
