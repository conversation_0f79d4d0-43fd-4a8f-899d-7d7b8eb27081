#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化测试运行器
无交互模式，所有配置从配置文件读取，自动执行测试并生成HTML报告
"""

import os
import sys
import json
from datetime import datetime
from typing import Dict, List, Any

from execution_config_reader import ExecutionConfigReader
from futures_config_reader import FuturesConfigReader
from futures_test_executor import FuturesTestExecutor
from enhanced_html_reporter import EnhancedHTMLReporter

class AutomatedTestRunner:
    """自动化测试运行器"""
    
    def __init__(self):
        print("初始化CTP期货交易自动化测试系统...")
        
        try:
            # 加载配置
            self.exec_config_reader = ExecutionConfigReader()
            self.futures_config_reader = FuturesConfigReader()
            self.test_executor = FuturesTestExecutor(self.futures_config_reader)
            self.html_reporter = EnhancedHTMLReporter(self.exec_config_reader)
            
            # 获取执行配置
            self.exec_config = self.exec_config_reader.get_execution_config()
            self.env_config = self.exec_config_reader.get_environment_config()
            
            print("✓ 系统初始化完成")
            
        except Exception as e:
            print(f"✗ 系统初始化失败: {e}")
            sys.exit(1)
    
    def run(self):
        """运行测试"""
        print(f"\n{'='*80}")
        print("CTP期货交易自动化测试系统")
        print(f"执行模式: {self.exec_config.mode}")
        print(f"执行目标: {self.exec_config.target}")
        print(f"{'='*80}")

        # 显示环境配置信息
        self._show_environment_info()
        
        start_time = datetime.now()
        results = []
        
        try:
            if self.exec_config.mode == "scenario":
                # 执行单个场景
                results = self._execute_single_scenario()
            elif self.exec_config.mode == "case":
                # 执行单个案例
                results = self._execute_single_case()
            elif self.exec_config.mode == "full":
                # 执行完整计划
                results = self._execute_full_plan()
            else:
                raise ValueError(f"不支持的执行模式: {self.exec_config.mode}")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 显示执行摘要
            self._show_execution_summary(results, duration)
            
            # 生成报告
            if self.exec_config.generate_report:
                self._generate_reports(results)
            
            print(f"\n✓ 测试执行完成，总耗时: {duration:.2f}秒")
            
        except Exception as e:
            print(f"\n✗ 测试执行失败: {e}")
            sys.exit(1)
    
    def _execute_single_scenario(self) -> List[Dict[str, Any]]:
        """执行单个场景"""
        scenario_id = self.exec_config.target
        
        print(f"\n执行场景: {scenario_id}")
        scenario = self.futures_config_reader.get_test_scenario(scenario_id)
        
        if not scenario:
            raise ValueError(f"场景不存在: {scenario_id}")
        
        print(f"场景名称: {scenario.scenario_name}")
        print(f"场景描述: {scenario.description}")
        print(f"包含案例: {', '.join(scenario.test_cases)}")
        
        result = self.test_executor.execute_scenario(scenario_id)
        return [result] if result else []
    
    def _execute_single_case(self) -> List[Dict[str, Any]]:
        """执行单个案例"""
        case_id = self.exec_config.target
        
        print(f"\n执行案例: {case_id}")
        test_case = self.futures_config_reader.get_test_case(case_id)
        
        if not test_case:
            raise ValueError(f"测试案例不存在: {case_id}")
        
        print(f"案例名称: {test_case.test_name}")
        print(f"案例描述: {test_case.description}")
        print(f"步骤数量: {len(test_case.steps)}")
        
        # 将单个案例包装成场景格式
        case_result = self.test_executor.execute_test_case(case_id)
        
        if case_result:
            scenario_result = {
                'scenario_info': {
                    'scenario_id': f'SINGLE_CASE_{case_id}',
                    'scenario_name': f'单案例执行: {test_case.test_name}',
                    'description': test_case.description,
                    'category': '单案例测试'
                },
                'start_time': case_result.get('start_time'),
                'end_time': case_result.get('end_time'),
                'duration': case_result.get('duration'),
                'test_case_results': [case_result],
                'summary': {
                    'total_cases': 1,
                    'total_steps': case_result.get('summary', {}).get('total_steps', 0),
                    'success_steps': case_result.get('summary', {}).get('success_steps', 0),
                    'failed_steps': case_result.get('summary', {}).get('failed_steps', 0),
                    'overall_success_rate': case_result.get('summary', {}).get('success_rate', 0)
                }
            }
            return [scenario_result]
        
        return []
    
    def _execute_full_plan(self) -> List[Dict[str, Any]]:
        """执行完整计划"""
        execution_order = self.exec_config_reader.get_execution_order()
        
        print(f"\n执行完整测试计划")
        print(f"计划场景数: {len(execution_order)}")
        print(f"执行顺序: {', '.join(execution_order)}")
        
        results = []
        
        for i, scenario_id in enumerate(execution_order, 1):
            print(f"\n[{i}/{len(execution_order)}] 执行场景: {scenario_id}")
            
            scenario = self.futures_config_reader.get_test_scenario(scenario_id)
            if scenario:
                print(f"场景名称: {scenario.scenario_name}")
                
                try:
                    result = self.test_executor.execute_scenario(scenario_id)
                    if result:
                        results.append(result)
                        
                        # 显示简要结果
                        summary = result.get('summary', {})
                        success_rate = summary.get('overall_success_rate', 0)
                        print(f"✓ 场景完成 - 成功率: {success_rate:.1f}%")
                    
                except Exception as e:
                    print(f"✗ 场景执行失败: {e}")
                    # 创建失败结果记录
                    error_result = {
                        'scenario_info': {
                            'scenario_id': scenario_id,
                            'scenario_name': scenario.scenario_name if scenario else scenario_id
                        },
                        'error': str(e),
                        'duration': 0.0,
                        'test_case_results': [],
                        'summary': {
                            'total_cases': 0,
                            'total_steps': 0,
                            'success_steps': 0,
                            'failed_steps': 0,
                            'overall_success_rate': 0
                        }
                    }
                    results.append(error_result)
            else:
                print(f"✗ 场景不存在: {scenario_id}")
            
            # 场景间隔
            if i < len(execution_order):
                print("等待执行下一个场景...")
                import time
                time.sleep(1)
        
        return results

    def _show_environment_info(self):
        """显示环境配置信息"""
        print(f"\n环境配置信息:")
        print(f"  交易前置机: {self.env_config.ctp_config.get('trade_front', 'N/A')}")
        print(f"  行情前置机: {self.env_config.ctp_config.get('md_front', 'N/A')}")
        print(f"  经纪商ID: {self.env_config.ctp_config.get('broker_id', 'N/A')}")
        print(f"  用户ID: {self.env_config.ctp_config.get('user_id', 'N/A')}")
        print(f"  投资者ID: {self.env_config.ctp_config.get('investor_id', 'N/A')}")
        print(f"  超时时间: {self.env_config.system.get('timeout', 30)}秒")
        print(f"  重试次数: {self.env_config.system.get('retry_count', 3)}")
        print(f"  日志级别: {self.env_config.system.get('log_level', 'INFO')}")

        # 显示测试数据配置
        test_data = self.exec_config_reader.get_test_data_config()
        print(f"\n测试数据配置:")
        print(f"  初始资金: {test_data.account.get('initial_fund', 0):,.2f}")
        print(f"  可用资金: {test_data.account.get('available_fund', 0):,.2f}")
        print(f"  已用保证金: {test_data.account.get('margin_used', 0):,.2f}")
        print(f"  测试合约数: {len(test_data.instruments)}")

        # 显示合约信息
        if test_data.instruments:
            print(f"  测试合约:")
            for instrument_id, instrument_info in test_data.instruments.items():
                print(f"    {instrument_id}: {instrument_info.get('name', 'N/A')} "
                      f"({instrument_info.get('exchange', 'N/A')})")

    def _show_execution_summary(self, results: List[Dict[str, Any]], duration: float):
        """显示执行摘要"""
        total_scenarios = len(results)
        total_cases = sum(len(r.get('test_case_results', [])) for r in results)
        total_steps = sum(r.get('summary', {}).get('total_steps', 0) for r in results)
        success_steps = sum(r.get('summary', {}).get('success_steps', 0) for r in results)
        
        overall_success_rate = (success_steps / total_steps * 100) if total_steps > 0 else 0
        
        print(f"\n{'='*80}")
        print("执行摘要")
        print(f"{'='*80}")
        print(f"执行场景数: {total_scenarios}")
        print(f"执行案例数: {total_cases}")
        print(f"执行步骤数: {total_steps}")
        print(f"成功步骤数: {success_steps}")
        print(f"整体成功率: {overall_success_rate:.1f}%")
        print(f"总执行时间: {duration:.2f}秒")
        print(f"{'='*80}")
        
        # 显示各场景结果
        for i, result in enumerate(results, 1):
            scenario_info = result.get('scenario_info', {})
            summary = result.get('summary', {})
            
            status = "✓" if summary.get('overall_success_rate', 0) >= 50 else "✗"
            print(f"{status} 场景{i}: {scenario_info.get('scenario_name', 'N/A')} "
                  f"(成功率: {summary.get('overall_success_rate', 0):.1f}%)")
    
    def _generate_reports(self, results: List[Dict[str, Any]]):
        """生成报告"""
        print(f"\n生成测试报告...")
        
        try:
            # 生成HTML报告
            html_path = self.html_reporter.generate_scenario_report(results)
            print(f"✓ HTML报告已生成: {html_path}")
            
            # 保存JSON结果
            if self.exec_config.save_json:
                json_path = self._save_json_results(results)
                print(f"✓ JSON结果已保存: {json_path}")
            
        except Exception as e:
            print(f"✗ 报告生成失败: {e}")
    
    def _save_json_results(self, results: List[Dict[str, Any]]) -> str:
        """保存JSON结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"futures_test_results_{timestamp}.json"
        json_path = os.path.join(self.env_config.directories.get('result', './result'), filename)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(json_path), exist_ok=True)
        
        # 保存结果
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        return json_path


def main():
    """主函数"""
    runner = AutomatedTestRunner()
    runner.run()


if __name__ == "__main__":
    main()
