#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML测试报告生成器
生成美观的HTML格式测试报告
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

class HTMLReporter:
    """HTML报告生成器"""
    
    def __init__(self):
        self.template_dir = "templates"
        self.output_dir = "result"
        
    def generate_test_case_report(self, test_result: Dict[str, Any], output_path: str = None) -> str:
        """生成测试案例HTML报告"""
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"{self.output_dir}/test_case_report_{timestamp}.html"
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        html_content = self._generate_case_html(test_result)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return output_path
    
    def generate_scenario_report(self, scenario_results: List[Dict[str, Any]], output_path: str = None) -> str:
        """生成场景测试HTML报告"""
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"{self.output_dir}/scenario_report_{timestamp}.html"
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        html_content = self._generate_scenario_html(scenario_results)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return output_path
    
    def _generate_case_html(self, test_result: Dict[str, Any]) -> str:
        """生成测试案例HTML内容"""
        case_info = test_result.get('test_case_info', {})
        summary = test_result.get('summary', {})
        step_results = test_result.get('step_results', [])
        
        # 计算统计信息
        total_steps = summary.get('total_steps', 0)
        success_steps = summary.get('success_steps', 0)
        failed_steps = summary.get('failed_steps', 0)
        success_rate = summary.get('success_rate', 0)
        duration = test_result.get('duration', 0)
        
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTP测试案例报告 - {case_info.get('test_name', '未知测试')}</title>
    <style>
        {self._get_css_styles()}
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>CTP期货交易自动化测试报告</h1>
            <div class="report-info">
                <span>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</span>
                <span>报告类型: 测试案例报告</span>
            </div>
        </header>
        
        <section class="test-overview">
            <h2>测试概览</h2>
            <div class="overview-grid">
                <div class="overview-item">
                    <div class="overview-label">测试案例</div>
                    <div class="overview-value">{case_info.get('test_name', '未知测试')}</div>
                </div>
                <div class="overview-item">
                    <div class="overview-label">测试ID</div>
                    <div class="overview-value">{case_info.get('test_id', 'N/A')}</div>
                </div>
                <div class="overview-item">
                    <div class="overview-label">测试分类</div>
                    <div class="overview-value">{case_info.get('category', 'N/A')}</div>
                </div>
                <div class="overview-item">
                    <div class="overview-label">优先级</div>
                    <div class="overview-value">{case_info.get('priority', 'N/A')}</div>
                </div>
            </div>
            <div class="description">
                <strong>测试描述:</strong> {case_info.get('description', '无描述')}
            </div>
        </section>
        
        <section class="test-statistics">
            <h2>执行统计</h2>
            <div class="stats-grid">
                <div class="stat-card success">
                    <div class="stat-number">{success_steps}</div>
                    <div class="stat-label">成功步骤</div>
                </div>
                <div class="stat-card failed">
                    <div class="stat-number">{failed_steps}</div>
                    <div class="stat-label">失败步骤</div>
                </div>
                <div class="stat-card total">
                    <div class="stat-number">{total_steps}</div>
                    <div class="stat-label">总步骤数</div>
                </div>
                <div class="stat-card rate">
                    <div class="stat-number">{success_rate:.1f}%</div>
                    <div class="stat-label">成功率</div>
                </div>
            </div>
            <div class="execution-info">
                <div class="info-item">
                    <strong>开始时间:</strong> {test_result.get('start_time', 'N/A')}
                </div>
                <div class="info-item">
                    <strong>结束时间:</strong> {test_result.get('end_time', 'N/A')}
                </div>
                <div class="info-item">
                    <strong>总耗时:</strong> {duration:.2f} 秒
                </div>
            </div>
        </section>
        
        <section class="step-details">
            <h2>步骤详情</h2>
            <div class="steps-container">
        """
        
        # 添加步骤详情
        for i, step in enumerate(step_results, 1):
            status_class = "success" if step.get('status') == '成功' else "failed"
            status_icon = "✓" if step.get('status') == '成功' else "✗"
            
            html += f"""
                <div class="step-card {status_class}">
                    <div class="step-header">
                        <div class="step-number">{i}</div>
                        <div class="step-title">
                            <h3>{step.get('step_name', '未知步骤')}</h3>
                            <div class="step-status {status_class}">
                                <span class="status-icon">{status_icon}</span>
                                <span class="status-text">{step.get('status', '未知')}</span>
                            </div>
                        </div>
                        <div class="step-duration">{step.get('duration', 0):.3f}s</div>
                    </div>
                    <div class="step-content">
                        <div class="step-info">
                            <strong>步骤ID:</strong> {step.get('step_id', 'N/A')}
                        </div>
            """
            
            # 添加实际结果
            actual_result = step.get('actual_result', {})
            if actual_result:
                html += '<div class="result-section"><h4>执行结果:</h4><div class="result-content">'
                for key, value in actual_result.items():
                    if key == 'raw_output':
                        continue
                    html += f'<div class="result-item"><strong>{key}:</strong> {value}</div>'
                html += '</div></div>'
            
            # 添加错误信息
            error_msg = step.get('error_message', '')
            if error_msg:
                html += f'<div class="error-section"><h4>错误信息:</h4><div class="error-content">{error_msg}</div></div>'
            
            html += """
                    </div>
                </div>
            """
        
        html += """
            </div>
        </section>
        
        <footer class="footer">
            <p>CTP期货交易自动化测试系统 - 测试报告</p>
            <p>生成时间: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</p>
        </footer>
    </div>
</body>
</html>
        """
        
        return html
    
    def _generate_scenario_html(self, scenario_results: List[Dict[str, Any]]) -> str:
        """生成场景测试HTML内容"""
        # 计算总体统计
        total_scenarios = len(scenario_results)
        total_cases = sum(r.get('summary', {}).get('total_cases', 0) for r in scenario_results)
        total_steps = sum(r.get('summary', {}).get('total_steps', 0) for r in scenario_results)
        success_steps = sum(r.get('summary', {}).get('success_steps', 0) for r in scenario_results)
        overall_success_rate = (success_steps / total_steps * 100) if total_steps > 0 else 0
        total_duration = sum(r.get('duration', 0) for r in scenario_results)
        
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTP场景测试报告</title>
    <style>
        {self._get_css_styles()}
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>CTP期货交易自动化测试报告</h1>
            <div class="report-info">
                <span>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</span>
                <span>报告类型: 场景测试报告</span>
            </div>
        </header>
        
        <section class="test-statistics">
            <h2>总体统计</h2>
            <div class="stats-grid">
                <div class="stat-card total">
                    <div class="stat-number">{total_scenarios}</div>
                    <div class="stat-label">测试场景</div>
                </div>
                <div class="stat-card total">
                    <div class="stat-number">{total_cases}</div>
                    <div class="stat-label">测试案例</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-number">{success_steps}</div>
                    <div class="stat-label">成功步骤</div>
                </div>
                <div class="stat-card rate">
                    <div class="stat-number">{overall_success_rate:.1f}%</div>
                    <div class="stat-label">整体成功率</div>
                </div>
            </div>
            <div class="execution-info">
                <div class="info-item">
                    <strong>总步骤数:</strong> {total_steps}
                </div>
                <div class="info-item">
                    <strong>总耗时:</strong> {total_duration:.2f} 秒
                </div>
                <div class="info-item">
                    <strong>平均每场景:</strong> {total_duration/total_scenarios:.2f} 秒
                </div>
            </div>
        </section>
        
        <section class="scenario-details">
            <h2>场景详情</h2>
            <div class="scenarios-container">
        """
        
        # 添加场景详情
        for i, scenario in enumerate(scenario_results, 1):
            scenario_info = scenario.get('scenario_info', {})
            summary = scenario.get('summary', {})
            success_rate = summary.get('overall_success_rate', 0)
            status_class = "success" if success_rate >= 70 else "warning" if success_rate >= 50 else "failed"
            
            html += f"""
                <div class="scenario-card {status_class}">
                    <div class="scenario-header">
                        <div class="scenario-number">{i}</div>
                        <div class="scenario-title">
                            <h3>{scenario_info.get('scenario_name', '未知场景')}</h3>
                            <div class="scenario-id">{scenario_info.get('scenario_id', 'N/A')}</div>
                        </div>
                        <div class="scenario-rate">{success_rate:.1f}%</div>
                    </div>
                    <div class="scenario-content">
                        <div class="scenario-description">
                            <strong>描述:</strong> {scenario_info.get('description', '无描述')}
                        </div>
                        <div class="scenario-stats">
                            <div class="stat-item">
                                <strong>测试案例:</strong> {summary.get('total_cases', 0)}
                            </div>
                            <div class="stat-item">
                                <strong>总步骤:</strong> {summary.get('total_steps', 0)}
                            </div>
                            <div class="stat-item">
                                <strong>成功步骤:</strong> {summary.get('success_steps', 0)}
                            </div>
                            <div class="stat-item">
                                <strong>耗时:</strong> {scenario.get('duration', 0):.2f}s
                            </div>
                        </div>
                    </div>
                </div>
            """
        
        html += """
            </div>
        </section>
        
        <footer class="footer">
            <p>CTP期货交易自动化测试系统 - 场景测试报告</p>
            <p>生成时间: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</p>
        </footer>
    </div>
</body>
</html>
        """
        
        return html

    def _get_css_styles(self) -> str:
        """获取CSS样式"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .report-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            font-size: 1.1em;
            opacity: 0.9;
        }

        .test-overview {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-overview h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .overview-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .overview-label {
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
        }

        .overview-value {
            font-size: 1.2em;
            color: #333;
        }

        .description {
            padding: 20px;
            background: #e8f4fd;
            border-radius: 8px;
            border-left: 4px solid #3182ce;
        }

        .test-statistics {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-statistics h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            color: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .stat-card.success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .stat-card.failed {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        .stat-card.total {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        }

        .stat-card.rate {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .stat-card.warning {
            background: linear-gradient(135deg, #ecc94b 0%, #d69e2e 100%);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .execution-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .info-item {
            font-size: 1.1em;
        }

        .step-details, .scenario-details {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .step-details h2, .scenario-details h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .steps-container, .scenarios-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .step-card, .scenario-card {
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .step-card.success {
            border-left: 5px solid #48bb78;
        }

        .step-card.failed {
            border-left: 5px solid #f56565;
        }

        .scenario-card.success {
            border-left: 5px solid #48bb78;
        }

        .scenario-card.warning {
            border-left: 5px solid #ecc94b;
        }

        .scenario-card.failed {
            border-left: 5px solid #f56565;
        }

        .step-header, .scenario-header {
            display: flex;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e2e8f0;
        }

        .step-number, .scenario-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 20px;
        }

        .step-title, .scenario-title {
            flex: 1;
        }

        .step-title h3, .scenario-title h3 {
            margin-bottom: 5px;
            color: #2d3748;
        }

        .step-status {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .step-status.success {
            color: #38a169;
        }

        .step-status.failed {
            color: #e53e3e;
        }

        .status-icon {
            font-weight: bold;
            font-size: 1.2em;
        }

        .step-duration, .scenario-rate {
            font-weight: bold;
            font-size: 1.1em;
            color: #4a5568;
        }

        .step-content, .scenario-content {
            padding: 20px;
        }

        .step-info, .scenario-description {
            margin-bottom: 15px;
            color: #4a5568;
        }

        .scenario-id {
            color: #718096;
            font-size: 0.9em;
        }

        .scenario-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-item {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .result-section, .error-section {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
        }

        .result-section {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
        }

        .error-section {
            background: #fed7d7;
            border: 1px solid #feb2b2;
        }

        .result-section h4, .error-section h4 {
            margin-bottom: 10px;
            color: #2d3748;
        }

        .result-content, .error-content {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .result-item {
            margin-bottom: 5px;
            padding: 5px;
            background: rgba(255,255,255,0.5);
            border-radius: 3px;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #718096;
            border-top: 1px solid #e2e8f0;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .report-info {
                flex-direction: column;
                gap: 10px;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .step-header, .scenario-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
        """
