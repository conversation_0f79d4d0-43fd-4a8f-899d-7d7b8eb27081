import json
import csv
import os
from datetime import datetime
from typing import Dict, List, Any
import pytest

class TestResultLogger:
    """测试结果记录器"""
    
    def __init__(self, test_name: str = None):
        self.test_name = test_name or "trading_test"
        self.results = []
        self.start_time = datetime.now()
        self.test_session_id = self.start_time.strftime("%Y%m%d_%H%M%S")
        
        # 创建结果目录
        self.results_dir = "test_results"
        os.makedirs(self.results_dir, exist_ok=True)
    
    def log_trading_verification(self, test_case: str, initial_state: Dict, 
                               final_state: Dict, trading_actions: List[Dict]):
        """记录交易验证结果"""
        verification_result = {
            "test_case": test_case,
            "timestamp": datetime.now().isoformat(),
            "initial_state": initial_state,
            "final_state": final_state,
            "trading_actions": trading_actions,
            "verification": self._calculate_verification(initial_state, final_state, trading_actions)
        }
        
        self.results.append(verification_result)
        return verification_result
    
    def _calculate_verification(self, initial: Dict, final: Dict, actions: List[Dict]) -> Dict:
        """计算验证结果"""
        verification = {
            "position_changes": {},
            "balance_changes": {},
            "margin_changes": {},
            "pnl_calculation": {},
            "success": True,
            "errors": []
        }
        
        try:
            # 持仓变化验证
            initial_positions = initial.get('positions', {})
            final_positions = final.get('positions', {})
            
            for instrument in set(list(initial_positions.keys()) + list(final_positions.keys())):
                initial_pos = initial_positions.get(instrument, {'long': 0, 'short': 0})
                final_pos = final_positions.get(instrument, {'long': 0, 'short': 0})
                
                verification["position_changes"][instrument] = {
                    "long_change": final_pos.get('long', 0) - initial_pos.get('long', 0),
                    "short_change": final_pos.get('short', 0) - initial_pos.get('short', 0),
                    "expected_change": self._calculate_expected_position_change(instrument, actions)
                }
            
            # 资金变化验证
            initial_balance = initial.get('account', {}).get('balance', 0)
            final_balance = final.get('account', {}).get('balance', 0)
            initial_available = initial.get('account', {}).get('available', 0)
            final_available = final.get('account', {}).get('available', 0)
            
            verification["balance_changes"] = {
                "balance_change": final_balance - initial_balance,
                "available_change": final_available - initial_available,
                "margin_change": (initial_available - final_available) - (initial_balance - final_balance)
            }
            
            # 盈亏计算
            verification["pnl_calculation"] = self._calculate_pnl(actions)
            
        except Exception as e:
            verification["success"] = False
            verification["errors"].append(f"验证计算错误: {str(e)}")
        
        return verification
    
    def _calculate_expected_position_change(self, instrument: str, actions: List[Dict]) -> Dict:
        """计算预期持仓变化"""
        expected_long_change = 0
        expected_short_change = 0
        
        for action in actions:
            if action.get('instrument_id') == instrument:
                volume = action.get('volume', 0)
                direction = action.get('direction')
                offset_flag = action.get('offset_flag')
                
                if direction == "买":
                    if offset_flag == "开仓":
                        expected_long_change += volume
                    elif offset_flag == "平仓":
                        expected_short_change -= volume
                elif direction == "卖":
                    if offset_flag == "开仓":
                        expected_short_change += volume
                    elif offset_flag == "平仓":
                        expected_long_change -= volume
        
        return {
            "long": expected_long_change,
            "short": expected_short_change
        }
    
    def _calculate_pnl(self, actions: List[Dict]) -> Dict:
        """计算盈亏"""
        pnl_by_instrument = {}
        
        for action in actions:
            instrument = action.get('instrument_id')
            if instrument not in pnl_by_instrument:
                pnl_by_instrument[instrument] = {
                    "open_positions": [],
                    "close_positions": [],
                    "realized_pnl": 0
                }
            
            if action.get('offset_flag') == "开仓":
                pnl_by_instrument[instrument]["open_positions"].append(action)
            elif action.get('offset_flag') == "平仓":
                pnl_by_instrument[instrument]["close_positions"].append(action)
        
        return pnl_by_instrument
    
    def save_results(self, format_type: str = "all"):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format_type in ["json", "all"]:
            self._save_json_results(timestamp)
        
        if format_type in ["csv", "all"]:
            self._save_csv_results(timestamp)
        
        if format_type in ["html", "all"]:
            self._save_html_results(timestamp)
    
    def _save_json_results(self, timestamp: str):
        """保存JSON格式结果"""
        filename = f"{self.results_dir}/test_results_{self.test_name}_{timestamp}.json"
        
        summary = {
            "test_session_id": self.test_session_id,
            "test_name": self.test_name,
            "start_time": self.start_time.isoformat(),
            "end_time": datetime.now().isoformat(),
            "total_tests": len(self.results),
            "successful_tests": sum(1 for r in self.results if r.get('verification', {}).get('success', False)),
            "results": self.results
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"JSON结果已保存: {filename}")
    
    def _save_csv_results(self, timestamp: str):
        """保存CSV格式结果"""
        filename = f"{self.results_dir}/test_summary_{self.test_name}_{timestamp}.csv"
        
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                "测试用例", "时间戳", "成功", "持仓变化", "资金变化", "盈亏", "错误信息"
            ])
            
            for result in self.results:
                verification = result.get('verification', {})
                writer.writerow([
                    result.get('test_case', ''),
                    result.get('timestamp', ''),
                    verification.get('success', False),
                    str(verification.get('position_changes', {})),
                    str(verification.get('balance_changes', {})),
                    str(verification.get('pnl_calculation', {})),
                    '; '.join(verification.get('errors', []))
                ])
        
        print(f"CSV结果已保存: {filename}")
    
    def _save_html_results(self, timestamp: str):
        """保存HTML格式结果"""
        filename = f"{self.results_dir}/test_report_{self.test_name}_{timestamp}.html"
        
        html_content = self._generate_html_report()
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"HTML报告已保存: {filename}")
    
    def _generate_html_report(self) -> str:
        """生成HTML报告"""
        successful_tests = sum(1 for r in self.results if r.get('verification', {}).get('success', False))
        success_rate = (successful_tests / len(self.results) * 100) if self.results else 0
        
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>期货交易测试报告 - {self.test_name}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .test-result {{ border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }}
        .success {{ background-color: #d4edda; }}
        .failure {{ background-color: #f8d7da; }}
        .details {{ margin-top: 10px; font-size: 0.9em; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>期货交易测试报告</h1>
        <p>测试名称: {self.test_name}</p>
        <p>测试时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="summary">
        <h2>测试摘要</h2>
        <p>总测试数: {len(self.results)}</p>
        <p>成功测试: {successful_tests}</p>
        <p>成功率: {success_rate:.1f}%</p>
    </div>
    
    <div class="results">
        <h2>详细结果</h2>
"""
        
        for result in self.results:
            verification = result.get('verification', {})
            success = verification.get('success', False)
            css_class = 'success' if success else 'failure'
            
            html += f"""
        <div class="test-result {css_class}">
            <h3>{result.get('test_case', '未知测试')}</h3>
            <p>状态: {'成功' if success else '失败'}</p>
            <p>时间: {result.get('timestamp', '')}</p>
            
            <div class="details">
                <h4>持仓变化:</h4>
                <pre>{json.dumps(verification.get('position_changes', {}), ensure_ascii=False, indent=2)}</pre>
                
                <h4>资金变化:</h4>
                <pre>{json.dumps(verification.get('balance_changes', {}), ensure_ascii=False, indent=2)}</pre>
                
                <h4>盈亏计算:</h4>
                <pre>{json.dumps(verification.get('pnl_calculation', {}), ensure_ascii=False, indent=2)}</pre>
"""
            
            if verification.get('errors'):
                html += f"""
                <h4>错误信息:</h4>
                <ul>
"""
                for error in verification.get('errors', []):
                    html += f"<li>{error}</li>"
                html += "</ul>"
            
            html += "</div></div>"
        
        html += """
    </div>
</body>
</html>
"""
        return html

# pytest插件集成
@pytest.fixture(scope="session")
def test_logger():
    """测试日志记录器fixture"""
    return TestResultLogger("pytest_trading_tests")

def pytest_runtest_makereport(item, call):
    """pytest钩子函数，记录测试结果"""
    if call.when == "call":
        # 获取测试结果
        outcome = yield
        rep = outcome.get_result()
        
        # 如果测试有test_logger fixture，记录结果
        if hasattr(item, 'funcargs') and 'test_logger' in item.funcargs:
            logger = item.funcargs['test_logger']
            
            # 记录测试结果
            test_result = {
                "test_case": item.nodeid,
                "timestamp": datetime.now().isoformat(),
                "status": "passed" if rep.passed else "failed",
                "duration": rep.duration,
                "error": str(rep.longrepr) if rep.failed else None
            }
            
            logger.results.append(test_result)