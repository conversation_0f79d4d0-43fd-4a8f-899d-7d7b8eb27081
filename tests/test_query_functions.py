#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询功能测试
测试CTP各种查询接口的功能
"""
import time
import pytest
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)
sys.path.insert(0, os.path.join(parent_dir, 'trader'))

from trader.ctp_direct_wrapper import CTPDirectWrapper


class TestQueryFunctions:
    """查询功能测试类"""
    
    @pytest.fixture(scope="class")
    def ctp_service(self):
        """CTP服务fixture"""
        service = CTPDirectWrapper()
        
        # 初始化服务
        init_result = service.initialize()
        assert init_result, "CTP服务初始化失败"
        
        # 等待登录完成
        login_success = False
        for i in range(10):
            if service.is_logged_in():
                login_success = True
                break
            time.sleep(1)
        
        assert login_success, "CTP服务登录失败"
        print(f"✓ CTP服务初始化和登录成功")
        
        yield service
        
        # 清理资源
        try:
            service.cleanup()
        except:
            pass
    
    def test_query_trading_account(self, ctp_service):
        """测试资金账户查询"""
        service = ctp_service
        
        result = service.query_trading_account()
        
        # 验证查询成功
        assert result.get('success'), "资金账户查询应该成功"
        
        # 验证返回数据结构
        data_list = result.get('data', [])
        assert len(data_list) > 0, "资金账户数据不能为空"
        
        data = data_list[0]  # 取首个元素
        # 使用TradingAccountField结构体中定义的字段名
        required_fields = ['Available', 'Balance', 'CurrMargin', 'FrozenMargin']
        
        for field in required_fields:
            assert field in data, f"资金账户数据应包含{field}字段"
            assert isinstance(data[field], (int, float)), f"{field}应为数值类型"
        
        # 验证数据合理性
        assert data['Available'] >= 0, "可用资金不能为负"
        assert data['Balance'] >= 0, "账户余额不能为负"
        assert data['CurrMargin'] >= 0, "当前保证金不能为负"
        
        print(f"✓ 资金账户查询成功")
        print(f"  可用资金: {data['Available']}")
        print(f"  账户余额: {data['Balance']}")
        print(f"  当前保证金: {data['CurrMargin']}")
    
    def test_query_instrument(self, ctp_service):
        """测试合约查询"""
        service = ctp_service
        
        # 测试股指期货合约
        if_result = service.query_instrument(
            instrument_id="IF2512",
            exchange_id="CFFEX"
        )
        
        assert if_result.get('success'), "IF2512合约查询应该成功"
        
        if_data_list = if_result.get('data', [])
        assert len(if_data_list) > 0, "IF2512合约数据不能为空"
        
        if_data = if_data_list[0]  # 取首个元素
        # 使用InstrumentField结构体中定义的字段名
        assert if_data.get('InstrumentID') == 'IF2512', "合约ID应该匹配"
        assert if_data.get('ExchangeID') == 'CFFEX', "交易所ID应该匹配"
        
        print(f"✓ IF2512合约查询成功")
        print(f"  合约状态: {if_data.get('IsTrading')}")
        print(f"  合约乘数: {if_data.get('VolumeMultiple')}")
        print(f"  最小变动价位: {if_data.get('PriceTick')}")
    
    def test_query_investor_position(self, ctp_service):
        """测试持仓查询"""
        service = ctp_service
        
        # 查询所有持仓
        all_positions = service.query_investor_position()
        assert all_positions.get('success'), "持仓查询应该成功"
        
        positions_data = all_positions.get('data', [])
        assert isinstance(positions_data, list), "持仓数据应为列表"
        
        print(f"✓ 持仓查询成功，共{len(positions_data)}个持仓")
        
        # 验证持仓数据结构 - 使用InvestorPositionField字段名
        for pos in positions_data:
            required_fields = ['InstrumentID', 'Position', 'YdPosition', 'UseMargin']
            for field in required_fields:
                assert field in pos, f"持仓数据应包含{field}字段"
            
            # 计算多空持仓 - 修正方向判断
            direction = pos.get('PosiDirection', '1')
            volume = pos.get('Position', 0)
            
            if direction == '2':  # 多头
                print(f"  {pos['InstrumentID']}: 多{volume}手")
            elif direction == '3':  # 空头
                print(f"  {pos['InstrumentID']}: 空{volume}手")
            else:  # 净持仓
                print(f"  {pos['InstrumentID']}: 净{volume}手")
    
    def test_query_investor_position_detail(self, ctp_service):
        """测试持仓明细查询"""
        service = ctp_service
        
        # 查询所有持仓明细
        all_details = service.query_investor_position_detail()
        assert all_details.get('success'), "持仓明细查询应该成功"
        
        details_data = all_details.get('data', [])
        assert isinstance(details_data, list), "持仓明细数据应为列表"
        
        print(f"✓ 持仓明细查询成功，共{len(details_data)}条明细")
        
        # 验证明细数据结构 - 使用InvestorPositionDetailField字段名
        for detail in details_data:
            required_fields = ['InstrumentID', 'Direction', 'Volume', 'OpenPrice', 'PositionProfitByTrade']
            for field in required_fields:
                assert field in detail, f"持仓明细应包含{field}字段"
        
            print(f"  {detail['InstrumentID']} {detail['Direction']} {detail['Volume']}手")
        
        # 查询特定合约持仓明细
        if_detail = service.query_investor_position_detail(instrument_id="IF2501")
        assert if_detail.get('success'), "IF2501持仓明细查询应该成功"
        
        print(f"✓ IF2501持仓明细查询成功")
    
    def test_query_order(self, ctp_service):
        """测试报单查询"""
        service = ctp_service
        
        result = service.query_order()
        assert result.get('success'), "报单查询应该成功"
        
        orders_data = result.get('data', [])
        assert isinstance(orders_data, list), "报单数据应为列表"
        
        print(f"✓ 报单查询成功，共{len(orders_data)}个报单")
        
        # 验证报单数据结构 - 使用OrderField字段名
        for order in orders_data:
            required_fields = ['OrderRef', 'InstrumentID', 'Direction', 'VolumeTotalOriginal', 'OrderStatus']
            for field in required_fields:
                assert field in order, f"报单数据应包含{field}字段"
            
            print(f"  {order['InstrumentID']} {order['Direction']} {order['VolumeTotalOriginal']}手 {order['OrderStatus']}")
    
    def test_query_trade(self, ctp_service):
        """测试成交查询"""
        service = ctp_service
        
        result = service.query_trade()
        assert result.get('success'), "成交查询应该成功"
        
        trades_data = result.get('data', [])
        assert isinstance(trades_data, list), "成交数据应为列表"
        
        print(f"✓ 成交查询成功，共{len(trades_data)}条成交")
        
        # 验证成交数据结构 - 使用TradeField字段名
        for trade in trades_data:
            required_fields = ['TradeID', 'InstrumentID', 'Direction', 'Volume', 'Price', 'TradeTime']
            for field in required_fields:
                assert field in trade, f"成交数据应包含{field}字段"
            
            print(f"  {trade['InstrumentID']} {trade['Direction']} {trade['Volume']}手@{trade['Price']}")
    
    def test_query_settlement_info(self, ctp_service):
        """测试结算单查询"""
        service = ctp_service
        
        # 查询当日结算单
        result = service.query_settlement_info()
        
        if result.get('success'):
            settlement_data = result.get('data', '')
            assert isinstance(settlement_data, str), "结算单数据应为字符串"
            
            print(f"✓ 结算单查询成功")
            print(f"  结算单长度: {len(settlement_data)}字符")
            
            # 验证结算单包含关键信息
            key_words = ['资金状况', '持仓汇总', '成交记录']
            for word in key_words:
                if word in settlement_data:
                    print(f"  包含{word}信息")
        else:
            print("⚠ 结算单查询失败或无数据")
    
    @pytest.mark.parametrize("query_type", [
        "trading_account",
        "investor_position", 
        "position_detail",
        "order",
        "trade"
    ])
    def test_query_response_time(self, ctp_service, query_type):
        """测试查询响应时间"""
        import time
        
        service = ctp_service
        
        start_time = time.time()
        
        if query_type == "trading_account":
            result = service.query_trading_account()
        elif query_type == "investor_position":
            result = service.query_investor_position()
        elif query_type == "position_detail":
            result = service.query_investor_position_detail()
        elif query_type == "order":
            result = service.query_order()
        elif query_type == "trade":
            result = service.query_trade()
        
        end_time = time.time()
        response_time = end_time - start_time
        
        assert result.get('success'), f"{query_type}查询应该成功"
        assert response_time < 5.0, f"{query_type}查询响应时间应小于5秒，实际{response_time:.2f}秒"
        
        print(f"✓ {query_type}查询响应时间: {response_time:.3f}秒")
    
    def test_query_data_consistency(self, ctp_service):
        """测试查询数据一致性"""
        service = ctp_service
        
        # 查询资金账户
        account_result = service.query_trading_account()
        account_data_list = account_result.get('data', [])
        account_margin = 0
        if len(account_data_list) > 0:
            account_data = account_data_list[0]  # 取首个元素
            account_margin = account_data.get('CurrMargin', 0)  # 使用正确字段名
        
        # 查询持仓
        position_result = service.query_investor_position()
        position_margin = 0
        
        for pos in position_result.get('data', []):
            position_margin += pos.get('UseMargin', 0)  # 使用正确字段名
        
        # 验证保证金数据一致性
        margin_diff = abs(account_margin - position_margin)
        tolerance = max(account_margin * 0.01, 100)  # 1%或100元的容差
        
        assert margin_diff <= tolerance, f"账户保证金{account_margin}与持仓保证金{position_margin}差异过大"
        
        print(f"✓ 数据一致性检查通过")
        print(f"  账户保证金: {account_margin}")
        print(f"  持仓保证金: {position_margin}")
        print(f"  差异: {margin_diff}")
