#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货交易自动化测试系统主程序
基于重新设计的期货测试配置，支持完整的交易闭环测试
"""

import os
import json
from datetime import datetime
from futures_config_reader import FuturesConfigReader
from futures_test_executor import FuturesTestExecutor
from html_reporter import HTMLReporter

class FuturesTestRunner:
    """期货测试运行器"""
    
    def __init__(self):
        try:
            self.config_reader = FuturesConfigReader()
            self.test_executor = FuturesTestExecutor(self.config_reader)
            self.html_reporter = HTMLReporter()
        except Exception as e:
            print(f"期货测试系统初始化失败: {e}")
            print("请检查期货CSV配置文件是否存在且格式正确")
            raise
    
    def show_main_menu(self):
        """显示主菜单"""
        print("\n" + "="*80)
        print("CTP期货交易自动化测试系统 (期货专业版)")
        print("="*80)
        print("1. 基础交易测试 - 执行开仓平仓基础交易流程")
        print("2. 风控测试 - 执行保证金和风险控制测试")
        print("3. 综合测试 - 执行完整的交易和风控综合测试")
        print("4. 单案例测试 - 执行单个测试案例")
        print("5. 自定义场景 - 自定义选择测试场景")
        print("6. 配置信息 - 查看期货测试配置")
        print("7. 执行计划 - 查看完整执行计划")
        print("0. 退出系统")
        print("="*80)
    
    def run_basic_trading_tests(self):
        """运行基础交易测试"""
        print("\n" + "-"*60)
        print("基础交易测试")
        print("-"*60)
        
        scenarios = self.config_reader.get_basic_trading_scenarios()
        
        print(f"基础交易场景 ({len(scenarios)} 个):")
        for i, scenario in enumerate(scenarios, 1):
            print(f"{i}. {scenario.scenario_name}")
            print(f"   描述: {scenario.description}")
            print(f"   测试案例: {', '.join(scenario.test_cases)}")
        
        print(f"\n{len(scenarios) + 1}. 执行所有基础交易场景")
        print(f"{len(scenarios) + 2}. 返回主菜单")
        
        try:
            choice = int(input(f"\n请选择 (1-{len(scenarios) + 2}): "))
            
            if choice == len(scenarios) + 2:
                return
            elif choice == len(scenarios) + 1:
                # 执行所有基础交易场景
                print(f"\n准备执行所有基础交易场景")
                confirm = input("确认执行? (y/n): ").strip().lower()
                if confirm == 'y':
                    results = self.execute_scenarios(scenarios)
                    self.save_scenario_results(results, "basic_trading")
                    input("\n按回车键继续...")
            elif 1 <= choice <= len(scenarios):
                selected_scenario = scenarios[choice - 1]
                print(f"\n准备执行: {selected_scenario.scenario_name}")
                confirm = input("确认执行? (y/n): ").strip().lower()
                if confirm == 'y':
                    result = self.test_executor.execute_scenario(selected_scenario.scenario_id)
                    self.save_scenario_results([result], "basic_trading")
                    input("\n按回车键继续...")
            else:
                print("无效选择")
                
        except ValueError:
            print("请输入有效数字")
        except KeyboardInterrupt:
            print("\n返回主菜单")
    
    def run_risk_control_tests(self):
        """运行风控测试"""
        print("\n" + "-"*60)
        print("风控测试")
        print("-"*60)
        
        scenarios = self.config_reader.get_risk_control_scenarios()
        
        print(f"风控测试场景 ({len(scenarios)} 个):")
        for i, scenario in enumerate(scenarios, 1):
            print(f"{i}. {scenario.scenario_name}")
            print(f"   描述: {scenario.description}")
            print(f"   测试案例: {', '.join(scenario.test_cases)}")
            if scenario.depends_on:
                print(f"   依赖场景: {', '.join(scenario.depends_on)}")
        
        print(f"\n{len(scenarios) + 1}. 执行所有风控测试场景")
        print(f"{len(scenarios) + 2}. 返回主菜单")
        
        try:
            choice = int(input(f"\n请选择 (1-{len(scenarios) + 2}): "))
            
            if choice == len(scenarios) + 2:
                return
            elif choice == len(scenarios) + 1:
                # 执行所有风控测试场景
                print(f"\n准备执行所有风控测试场景")
                print("注意: 风控测试需要先执行基础交易场景作为前置条件")
                confirm = input("确认执行? (y/n): ").strip().lower()
                if confirm == 'y':
                    results = self.execute_scenarios(scenarios)
                    self.save_scenario_results(results, "risk_control")
                    input("\n按回车键继续...")
            elif 1 <= choice <= len(scenarios):
                selected_scenario = scenarios[choice - 1]
                print(f"\n准备执行: {selected_scenario.scenario_name}")
                if selected_scenario.depends_on:
                    print(f"依赖场景: {', '.join(selected_scenario.depends_on)}")
                    print("请确保依赖场景已经执行完成")
                confirm = input("确认执行? (y/n): ").strip().lower()
                if confirm == 'y':
                    result = self.test_executor.execute_scenario(selected_scenario.scenario_id)
                    self.save_scenario_results([result], "risk_control")
                    input("\n按回车键继续...")
            else:
                print("无效选择")
                
        except ValueError:
            print("请输入有效数字")
        except KeyboardInterrupt:
            print("\n返回主菜单")
    
    def run_comprehensive_tests(self):
        """运行综合测试"""
        print("\n" + "-"*60)
        print("综合测试")
        print("-"*60)
        
        scenarios = self.config_reader.get_comprehensive_scenarios()
        
        if not scenarios:
            print("没有配置综合测试场景")
            input("\n按回车键继续...")
            return
        
        print(f"综合测试场景 ({len(scenarios)} 个):")
        for i, scenario in enumerate(scenarios, 1):
            print(f"{i}. {scenario.scenario_name}")
            print(f"   描述: {scenario.description}")
            print(f"   测试案例: {', '.join(scenario.test_cases)}")
            print(f"   依赖场景: {', '.join(scenario.depends_on)}")
        
        print(f"\n{len(scenarios) + 1}. 执行完整测试计划")
        print(f"{len(scenarios) + 2}. 返回主菜单")
        
        try:
            choice = int(input(f"\n请选择 (1-{len(scenarios) + 2}): "))
            
            if choice == len(scenarios) + 2:
                return
            elif choice == len(scenarios) + 1:
                # 执行完整测试计划
                print(f"\n准备执行完整测试计划")
                print("这将按依赖关系执行所有测试场景")
                confirm = input("确认执行? (y/n): ").strip().lower()
                if confirm == 'y':
                    self.execute_full_test_plan()
                    input("\n按回车键继续...")
            elif 1 <= choice <= len(scenarios):
                selected_scenario = scenarios[choice - 1]
                print(f"\n准备执行: {selected_scenario.scenario_name}")
                print(f"依赖场景: {', '.join(selected_scenario.depends_on)}")
                print("请确保所有依赖场景已经执行完成")
                confirm = input("确认执行? (y/n): ").strip().lower()
                if confirm == 'y':
                    result = self.test_executor.execute_scenario(selected_scenario.scenario_id)
                    self.save_scenario_results([result], "comprehensive")
                    input("\n按回车键继续...")
            else:
                print("无效选择")
                
        except ValueError:
            print("请输入有效数字")
        except KeyboardInterrupt:
            print("\n返回主菜单")
    
    def execute_full_test_plan(self):
        """执行完整测试计划"""
        execution_plan = self.config_reader.get_execution_plan()
        all_results = []
        
        print(f"\n开始执行完整测试计划")
        print(f"共 {len(execution_plan)} 个执行阶段")
        print("="*80)
        
        for stage_num, stage_scenarios in enumerate(execution_plan, 1):
            print(f"\n执行阶段 {stage_num}: {len(stage_scenarios)} 个场景")
            print("-" * 60)
            
            stage_results = []
            for scenario in stage_scenarios:
                print(f"\n执行场景: {scenario.scenario_name}")
                try:
                    result = self.test_executor.execute_scenario(scenario.scenario_id)
                    stage_results.append(result)
                    all_results.append(result)
                    
                    # 显示简要结果
                    if result and 'summary' in result:
                        summary = result['summary']
                        print(f"✓ 场景完成 - 成功率: {summary['overall_success_rate']:.1f}%")
                    
                except Exception as e:
                    print(f"✗ 场景执行失败: {e}")
                    stage_results.append({
                        'scenario_info': {'scenario_id': scenario.scenario_id, 'scenario_name': scenario.scenario_name},
                        'error': str(e),
                        'duration': 0.0
                    })
            
            print(f"\n阶段 {stage_num} 完成，共执行 {len(stage_results)} 个场景")
            
            # 阶段间隔
            if stage_num < len(execution_plan):
                print("等待执行下一阶段...")
                import time
                time.sleep(2)
        
        # 保存完整测试结果
        self.save_scenario_results(all_results, "full_test_plan")
        
        # 显示总体统计
        total_scenarios = len(all_results)
        successful_scenarios = sum(1 for r in all_results if r.get('summary', {}).get('overall_success_rate', 0) >= 50)
        
        print(f"\n{'='*80}")
        print(f"完整测试计划执行完成")
        print(f"总场景数: {total_scenarios}")
        print(f"成功场景: {successful_scenarios}")
        print(f"场景成功率: {successful_scenarios/total_scenarios*100:.1f}%")
        print(f"{'='*80}")
    
    def execute_scenarios(self, scenarios):
        """执行多个场景"""
        results = []
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n[{i}/{len(scenarios)}] 执行场景: {scenario.scenario_name}")
            
            try:
                result = self.test_executor.execute_scenario(scenario.scenario_id)
                results.append(result)
                
                # 显示简要结果
                if result and 'summary' in result:
                    summary = result['summary']
                    print(f"✓ 场景完成 - 成功率: {summary['overall_success_rate']:.1f}%")
                
                # 场景间隔
                if i < len(scenarios):
                    import time
                    time.sleep(2)
                    
            except Exception as e:
                print(f"✗ 场景执行失败: {e}")
                results.append({
                    'scenario_info': {'scenario_id': scenario.scenario_id, 'scenario_name': scenario.scenario_name},
                    'error': str(e),
                    'duration': 0.0
                })
        
        return results
    
    def save_scenario_results(self, results, test_type):
        """保存场景测试结果"""
        if not results:
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON格式
        json_path = f"result/futures_{test_type}_results_{timestamp}.json"
        os.makedirs(os.path.dirname(json_path), exist_ok=True)
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 生成HTML报告
        try:
            html_path = self.html_reporter.generate_scenario_report(
                results, f"result/futures_{test_type}_report_{timestamp}.html"
            )
            print(f"HTML报告已生成: {html_path}")
            print(f"可以在浏览器中查看: {html_path}")
        except Exception as e:
            print(f"生成HTML报告失败: {e}")
        
        print(f"JSON结果已保存: {json_path}")
    
    def show_config_info(self):
        """显示配置信息"""
        print("\n" + "-"*60)
        print("期货测试配置信息")
        print("-"*60)
        
        # 验证配置依赖关系
        errors = self.config_reader.validate_scenario_dependencies()
        if errors:
            print("配置验证错误:")
            for error in errors:
                print(f"  ✗ {error}")
            print()
        else:
            print("✓ 配置验证通过")
        
        # 显示配置统计
        test_cases = self.config_reader.get_all_test_cases()
        scenarios = self.config_reader.get_all_test_scenarios()
        
        print(f"\n配置统计:")
        print(f"  测试案例: {len(test_cases)} 个")
        print(f"  测试场景: {len(scenarios)} 个")
        print(f"  预设条件: {len(self.config_reader.preconditions)} 个")
        print(f"  验证规则: {len(self.config_reader.validation_rules)} 个")
        
        # 按分类显示
        basic_cases = self.config_reader.get_test_cases_by_category("基础交易")
        risk_cases = self.config_reader.get_test_cases_by_category("风控测试")
        
        print(f"\n测试案例分类:")
        print(f"  基础交易: {len(basic_cases)} 个")
        print(f"  风控测试: {len(risk_cases)} 个")
        
        basic_scenarios = self.config_reader.get_basic_trading_scenarios()
        risk_scenarios = self.config_reader.get_risk_control_scenarios()
        comp_scenarios = self.config_reader.get_comprehensive_scenarios()
        
        print(f"\n测试场景分类:")
        print(f"  基础交易流程: {len(basic_scenarios)} 个")
        print(f"  风控测试: {len(risk_scenarios)} 个")
        print(f"  综合测试: {len(comp_scenarios)} 个")
        
        input("\n按回车键继续...")
    
    def show_execution_plan(self):
        """显示执行计划"""
        print("\n" + "-"*60)
        print("完整执行计划")
        print("-"*60)
        
        execution_plan = self.config_reader.get_execution_plan()
        
        print(f"执行计划概述:")
        print(f"  总阶段数: {len(execution_plan)}")
        print(f"  总场景数: {sum(len(stage) for stage in execution_plan)}")
        
        for stage_num, stage_scenarios in enumerate(execution_plan, 1):
            print(f"\n阶段 {stage_num}: {len(stage_scenarios)} 个场景")
            for scenario in stage_scenarios:
                print(f"  - {scenario.scenario_id}: {scenario.scenario_name}")
                print(f"    分类: {scenario.category}")
                print(f"    测试案例: {', '.join(scenario.test_cases)}")
                if scenario.depends_on:
                    print(f"    依赖: {', '.join(scenario.depends_on)}")
        
        input("\n按回车键继续...")
    
    def run(self):
        """运行主程序"""
        print("欢迎使用CTP期货交易自动化测试系统 (期货专业版)!")
        
        while True:
            try:
                self.show_main_menu()
                choice = input("\n请选择功能 (0-7): ").strip()
                
                if choice == '0':
                    print("\n感谢使用，再见!")
                    break
                elif choice == '1':
                    self.run_basic_trading_tests()
                elif choice == '2':
                    self.run_risk_control_tests()
                elif choice == '3':
                    self.run_comprehensive_tests()
                elif choice == '4':
                    # 单案例测试功能可以复用之前的代码
                    print("单案例测试功能开发中...")
                    input("按回车键继续...")
                elif choice == '5':
                    # 自定义场景功能可以复用之前的代码
                    print("自定义场景功能开发中...")
                    input("按回车键继续...")
                elif choice == '6':
                    self.show_config_info()
                elif choice == '7':
                    self.show_execution_plan()
                else:
                    print("无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                confirm = input("确认退出? (y/n): ").strip().lower()
                if confirm == 'y':
                    break
            except Exception as e:
                print(f"\n程序运行出错: {e}")
                print("请检查系统环境和配置")


def main():
    """主函数"""
    runner = FuturesTestRunner()
    runner.run()


if __name__ == "__main__":
    main()
