#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货测试配置读取器
专门读取期货测试相关的CSV配置文件
"""

import os
import csv
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
@dataclass
class TestStep:
    """测试步骤"""
    step_id: str
    step_name: str
    step_type: str
    step_description: str
    preconditions: List[str]
    trading_params: Dict[str, Any]
    expected_results: Dict[str, Any]
    validation_rules: List[str]

@dataclass
class TestCase:
    """测试案例"""
    test_id: str
    test_name: str
    description: str
    category: str
    priority: str
    steps: List[TestStep]

@dataclass
class TestScenario:
    """测试场景"""
    scenario_id: str
    scenario_name: str
    description: str
    category: str
    test_cases: List[str]
    preconditions: List[str]
    cleanup_actions: List[str]
    depends_on: List[str]
    execution_order: int

@dataclass
class Precondition:
    """预设条件"""
    condition_id: str
    condition_name: str
    condition_type: str
    check_method: str
    check_params: Dict[str, Any]
    expected_result: Dict[str, Any]
    error_message: str
    retry_count: int

@dataclass
class ValidationRule:
    """验证规则"""
    validation_id: str
    validation_name: str
    validation_type: str
    field_name: str
    operator: str
    expected_value: Any
    error_message: str
    is_required: bool

class FuturesConfigReader:
    """期货测试配置读取器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = config_dir
        self.test_cases: Dict[str, TestCase] = {}
        self.test_scenarios: Dict[str, TestScenario] = {}
        self.preconditions: Dict[str, Precondition] = {}
        self.validation_rules: Dict[str, ValidationRule] = {}
        
        self._load_futures_configs()
    
    def _load_futures_configs(self):
        """加载期货测试配置文件"""
        try:
            self._load_futures_validation_rules()
            self._load_futures_preconditions()
            self._load_futures_test_cases()
            self._load_futures_test_scenarios()
        except Exception as e:
            raise RuntimeError(f"加载期货CSV配置文件失败: {e}")
    
    def _load_futures_test_cases(self):
        """加载期货测试案例配置"""
        csv_path = os.path.join(self.config_dir, "../cases/futures_test_cases.csv")
        if not os.path.exists(csv_path):
            raise FileNotFoundError(f"期货测试案例配置文件不存在: {csv_path}")
        
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                test_id = row['test_id']
                
                # 解析交易参数 (使用|分隔符)
                trading_params = {}
                if row['trading_params']:
                    for param in row['trading_params'].split('|'):
                        if '=' in param:
                            key, value = param.split('=', 1)
                            trading_params[key] = self._convert_value(value)
                
                # 解析预期结果 (使用|分隔符)
                expected_results = {}
                if row['expected_results']:
                    for result in row['expected_results'].split('|'):
                        if '=' in result:
                            key, value = result.split('=', 1)
                            # 处理比较操作符
                            if '>=' in value:
                                op, val = '>=', value.replace('>=', '')
                                expected_results[key] = {'operator': op, 'value': self._convert_value(val)}
                            elif '<=' in value:
                                op, val = '<=', value.replace('<=', '')
                                expected_results[key] = {'operator': op, 'value': self._convert_value(val)}
                            elif '>' in value:
                                op, val = '>', value.replace('>', '')
                                expected_results[key] = {'operator': op, 'value': self._convert_value(val)}
                            elif '<' in value:
                                op, val = '<', value.replace('<', '')
                                expected_results[key] = {'operator': op, 'value': self._convert_value(val)}
                            else:
                                expected_results[key] = self._convert_value(value)
                
                # 创建测试步骤
                step = TestStep(
                    step_id=row['step_id'],
                    step_name=row['step_name'],
                    step_type=row['step_type'],
                    step_description=row['step_description'],
                    preconditions=row['preconditions'].split('|') if row['preconditions'] else [],
                    trading_params=trading_params,
                    expected_results=expected_results,
                    validation_rules=row['validation_rules'].split('|') if row['validation_rules'] else []
                )
                
                # 添加到测试案例
                if test_id not in self.test_cases:
                    self.test_cases[test_id] = TestCase(
                        test_id=test_id,
                        test_name=row['test_name'],
                        description=row['description'],
                        category=row['category'],
                        priority=row['priority'],
                        steps=[]
                    )
                
                self.test_cases[test_id].steps.append(step)
    
    def _load_futures_test_scenarios(self):
        """加载期货测试场景配置"""
        csv_path = os.path.join(self.config_dir, "../cases/futures_test_scenarios.csv")
        if not os.path.exists(csv_path):
            raise FileNotFoundError(f"期货测试场景配置文件不存在: {csv_path}")
        
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                scenario = TestScenario(
                    scenario_id=row['scenario_id'],
                    scenario_name=row['scenario_name'],
                    description=row['description'],
                    category=row['category'],
                    test_cases=row['test_cases'].split('|') if row['test_cases'] else [],
                    preconditions=row['preconditions'].split('|') if row['preconditions'] else [],
                    cleanup_actions=row['cleanup_actions'].split('|') if row['cleanup_actions'] else [],
                    depends_on=row['depends_on'].split('|') if row['depends_on'] else [],
                    execution_order=int(row['execution_order']) if row['execution_order'] else 0
                )
                
                self.test_scenarios[scenario.scenario_id] = scenario
    
    def _load_futures_preconditions(self):
        """加载期货预设条件配置"""
        csv_path = os.path.join(self.config_dir, "../cases/futures_preconditions.csv")
        if not os.path.exists(csv_path):
            raise FileNotFoundError(f"期货预设条件配置文件不存在: {csv_path}")
        
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                # 解析检查参数 (使用|分隔符)
                check_params = {}
                if row['check_params']:
                    for param in row['check_params'].split('|'):
                        if '=' in param:
                            key, value = param.split('=', 1)
                            check_params[key] = self._convert_value(value)
                
                # 解析预期结果 (使用|分隔符)
                expected_result = {}
                if row['expected_result']:
                    for result in row['expected_result'].split('|'):
                        if '=' in result:
                            key, value = result.split('=', 1)
                            expected_result[key] = self._convert_value(value)
                
                precondition = Precondition(
                    condition_id=row['condition_id'],
                    condition_name=row['condition_name'],
                    condition_type=row['condition_type'],
                    check_method=row['check_method'],
                    check_params=check_params,
                    expected_result=expected_result,
                    error_message=row['error_message'],
                    retry_count=int(row['retry_count']) if row['retry_count'] else 1
                )
                
                self.preconditions[precondition.condition_id] = precondition
    
    def _load_futures_validation_rules(self):
        """加载期货验证规则配置"""
        csv_path = os.path.join(self.config_dir, "../cases/futures_validations.csv")
        if not os.path.exists(csv_path):
            raise FileNotFoundError(f"期货验证规则配置文件不存在: {csv_path}")
        
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                validation = ValidationRule(
                    validation_id=row['validation_id'],
                    validation_name=row['validation_name'],
                    validation_type=row['validation_type'],
                    field_name=row['field_name'],
                    operator=row['operator'],
                    expected_value=self._convert_value(row['expected_value']),
                    error_message=row['error_message'],
                    is_required=row['is_required'].lower() == 'true'
                )
                
                self.validation_rules[validation.validation_id] = validation

    def _convert_value(self, value: str) -> Any:
        """转换字符串值为适当的类型"""
        if not value or value.lower() == 'null':
            return None
        elif value.lower() == 'true':
            return True
        elif value.lower() == 'false':
            return False
        else:
            try:
                if '.' in value:
                    return float(value)
                elif value.isdigit() or (value.startswith('-') and value[1:].isdigit()):
                    return int(value)
                else:
                    return value
            except:
                return value

    def get_test_case(self, test_id: str) -> Optional[TestCase]:
        """获取测试案例"""
        return self.test_cases.get(test_id)

    def get_test_scenario(self, scenario_id: str) -> Optional[TestScenario]:
        """获取测试场景"""
        return self.test_scenarios.get(scenario_id)

    def get_precondition(self, condition_id: str) -> Optional[Precondition]:
        """获取预设条件"""
        return self.preconditions.get(condition_id)

    def get_validation_rule(self, validation_id: str) -> Optional[ValidationRule]:
        """获取验证规则"""
        return self.validation_rules.get(validation_id)

    def get_all_test_cases(self) -> Dict[str, TestCase]:
        """获取所有测试案例"""
        return self.test_cases

    def get_all_test_scenarios(self) -> Dict[str, TestScenario]:
        """获取所有测试场景"""
        return self.test_scenarios

    def get_scenarios_by_order(self) -> List[TestScenario]:
        """按执行顺序获取测试场景"""
        scenarios = list(self.test_scenarios.values())
        return sorted(scenarios, key=lambda x: x.execution_order)
    
    def get_test_cases_by_category(self, category: str) -> Dict[str, TestCase]:
        """按分类获取测试案例"""
        return {test_id: test_case for test_id, test_case in self.test_cases.items() 
                if test_case.category == category}
    
    def get_scenarios_by_category(self, category: str) -> List[TestScenario]:
        """按分类获取测试场景"""
        scenarios = [scenario for scenario in self.test_scenarios.values() 
                    if scenario.category == category]
        return sorted(scenarios, key=lambda x: x.execution_order)
    
    def get_basic_trading_scenarios(self) -> List[TestScenario]:
        """获取基础交易场景"""
        return self.get_scenarios_by_category("基础交易流程")
    
    def get_risk_control_scenarios(self) -> List[TestScenario]:
        """获取风控测试场景"""
        return self.get_scenarios_by_category("风控测试")
    
    def get_comprehensive_scenarios(self) -> List[TestScenario]:
        """获取综合测试场景"""
        return self.get_scenarios_by_category("综合测试")
    
    def validate_scenario_dependencies(self) -> List[str]:
        """验证场景依赖关系"""
        errors = []
        
        for scenario in self.test_scenarios.values():
            # 检查依赖场景是否存在
            for dep in scenario.depends_on:
                if dep and dep not in self.test_scenarios:
                    errors.append(f"场景 {scenario.scenario_id} 依赖的场景 {dep} 不存在")
            
            # 检查测试案例是否存在
            for test_case_id in scenario.test_cases:
                if test_case_id not in self.test_cases:
                    errors.append(f"场景 {scenario.scenario_id} 引用的测试案例 {test_case_id} 不存在")
            
            # 检查执行顺序的合理性
            for dep in scenario.depends_on:
                if dep and dep in self.test_scenarios:
                    dep_scenario = self.test_scenarios[dep]
                    if dep_scenario.execution_order >= scenario.execution_order:
                        errors.append(f"场景 {scenario.scenario_id} 的执行顺序应该在依赖场景 {dep} 之后")
        
        return errors
    
    def get_execution_plan(self) -> List[List[TestScenario]]:
        """获取场景执行计划（按依赖关系分组）"""
        scenarios = list(self.test_scenarios.values())
        scenarios.sort(key=lambda x: x.execution_order)
        
        execution_plan = []
        current_group = []
        current_order = None
        
        for scenario in scenarios:
            if current_order is None or scenario.execution_order == current_order:
                current_group.append(scenario)
                current_order = scenario.execution_order
            else:
                if current_group:
                    execution_plan.append(current_group)
                current_group = [scenario]
                current_order = scenario.execution_order
        
        if current_group:
            execution_plan.append(current_group)
        
        return execution_plan


def main():
    """主函数"""
    print("期货测试配置读取器测试")
    print("=" * 50)
    
    try:
        reader = FuturesConfigReader()
        
        # 验证依赖关系
        errors = reader.validate_scenario_dependencies()
        if errors:
            print("配置验证错误:")
            for error in errors:
                print(f"  - {error}")
            return
        
        # 显示加载的配置
        print(f"成功加载期货测试配置:")
        print(f"  测试案例: {len(reader.test_cases)} 个")
        print(f"  测试场景: {len(reader.test_scenarios)} 个")
        print(f"  预设条件: {len(reader.preconditions)} 个")
        print(f"  验证规则: {len(reader.validation_rules)} 个")
        
        # 按分类显示测试案例
        basic_cases = reader.get_test_cases_by_category("基础交易")
        risk_cases = reader.get_test_cases_by_category("风控测试")
        
        print(f"\n基础交易案例 ({len(basic_cases)} 个):")
        for test_id, test_case in basic_cases.items():
            print(f"  {test_id}: {test_case.test_name} ({len(test_case.steps)} 个步骤)")
        
        print(f"\n风控测试案例 ({len(risk_cases)} 个):")
        for test_id, test_case in risk_cases.items():
            print(f"  {test_id}: {test_case.test_name} ({len(test_case.steps)} 个步骤)")
        
        # 显示执行计划
        execution_plan = reader.get_execution_plan()
        print(f"\n场景执行计划 ({len(execution_plan)} 个阶段):")
        for i, group in enumerate(execution_plan, 1):
            print(f"  阶段 {i}: {len(group)} 个场景")
            for scenario in group:
                print(f"    - {scenario.scenario_id}: {scenario.scenario_name}")
        
    except Exception as e:
        print(f"配置读取失败: {e}")


if __name__ == "__main__":
    main()
