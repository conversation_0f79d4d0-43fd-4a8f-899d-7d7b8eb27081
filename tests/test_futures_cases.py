#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货测试案例实现
基于cases/futures_test_cases.csv中定义的测试案例
"""

import pytest
import time
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)
sys.path.insert(0, os.path.join(parent_dir, 'trader'))

from trader.ctp_direct_wrapper import CTPDirectWrapper


class TestFuturesCases:
    """期货测试案例类"""
    
    @pytest.fixture(scope="class")
    def ctp_service(self):
        """CTP服务fixture"""
        service = CTPDirectWrapper()
        
        # 初始化服务
        init_result = service.initialize()
        assert init_result, "CTP服务初始化失败"
        
        # 等待连接和登录完成
        login_success = False
        for i in range(15):  # 增加等待时间
            if service.is_connected() and service.is_logged_in():
                login_success = True
                break
            time.sleep(1)
        
        assert login_success, "CTP服务连接或登录失败"
        print(f"✓ CTP服务初始化、连接和登录成功")
        
        yield service
        
        # 清理：平掉所有测试持仓
        self._cleanup_positions(service)
    
    def _cleanup_positions(self, service):
        """清理测试持仓"""
        positions = service.query_investor_position()
        if positions.get('success') and positions.get('data'):
            for pos in positions['data']:
                instrument_id = pos.get('InstrumentID')
                position_volume = pos.get('Position', 0)
                direction = pos.get('PosiDirection')
                
                if position_volume > 0:
                    if direction == '2':  # 多头持仓
                        service.insert_order_direct(
                            instrument_id=instrument_id,
                            direction="卖",
                            offset_flag="平仓",
                            price=4000.0 * 0.95,  # 使用固定价格
                            volume=position_volume
                        )
                    elif direction == '3':  # 空头持仓
                        service.insert_order_direct(
                            instrument_id=instrument_id,
                            direction="买",
                            offset_flag="平仓",
                            price=4000.0 * 1.05,  # 使用固定价格
                            volume=position_volume
                        )
    
    def test_tc_fu001_stock_index_futures_buy_open(self, ctp_service):
        """TC_FU001: 验证股指期货买入开仓功能"""
        service = ctp_service
        
        # STEP_001: 查询账户资金
        account_result = service.query_trading_account()
        assert account_result.get('success'), "账户查询失败"
        
        account_data_list = account_result.get('data', [])
        assert len(account_data_list) > 0, "账户数据不能为空"
        account_data = account_data_list[0]
        available_fund = account_data.get('Available', 0)  # 使用正确字段名
        assert available_fund >= 50000, f"可用资金{available_fund}不足50000"
        print(f"✓ 账户可用资金: {available_fund}")
        
        # STEP_003: 查询当前持仓
        before_position = service.query_investor_position(instrument_id="IF2501")
        assert before_position.get('success'), "持仓查询失败"
        
        before_long_volume = 0
        for pos in before_position.get('data', []):
            if pos.get('InstrumentID') == 'IF2501' and pos.get('PosiDirection') == '2':  # 多头
                before_long_volume = pos.get('Position', 0)
                break
        print(f"✓ 开仓前多头持仓: {before_long_volume}手")
        
        # STEP_006: 验证开仓结果
        after_position = service.query_investor_position(instrument_id="IF2501")
        assert after_position.get('success'), "开仓后持仓查询失败"
        
        after_long_volume = 0
        for pos in after_position.get('data', []):
            if pos.get('InstrumentID') == 'IF2501' and pos.get('PosiDirection') == '2':  # 多头
                after_long_volume = pos.get('Position', 0)
                break
        
        # 验证保证金占用
        after_account = service.query_trading_account()
        after_account_data = after_account.get('data', [{}])[0]
        after_available = after_account_data.get('Available', 0)
        margin_used = available_fund - after_available
        assert margin_used > 0, "应该有保证金占用"
        
        print(f"✓ 开仓成功，持仓增加到{after_long_volume}手，保证金占用{margin_used}")
    
    def test_tc_fu002_stock_index_futures_sell_close(self, ctp_service):
        """TC_FU002: 验证股指期货卖出平仓功能"""
        service = ctp_service
        
        # 先确保有多头持仓
        self._ensure_long_position(service, "IF2501", 1)
        
        # STEP_001: 查询持仓状态
        before_position = service.query_investor_position(instrument_id="IF2501")
        assert before_position.get('success'), "持仓查询失败"
        
        before_long_volume = 0
        for pos in before_position.get('data', []):
            if pos.get('InstrumentID') == 'IF2501' and pos.get('PosiDirection') == '2':  # 多头
                before_long_volume = pos.get('Position', 0)
                break
        
        assert before_long_volume > 0, "需要有多头持仓才能平仓"
        print(f"✓ 平仓前多头持仓: {before_long_volume}手")
        
        # STEP_002: 计算平仓保证金释放
        before_account = service.query_trading_account()
        before_account_data = before_account.get('data', [{}])[0]
        before_available = before_account_data.get('Available', 0)
        
        # STEP_003: 提交卖出平仓订单
        close_result = service.insert_order_direct(
            instrument_id="IF2501",
            exchange_id="CFFEX",
            direction="卖",
            offset_flag="平仓",
            price_type="LimitPrice",
            limit_price=4050.0,
            volume=1,
            hedge_flag="投机"
        )
        assert close_result is not None, "平仓订单提交失败"
        print(f"✓ 卖出平仓订单提交成功")
        
        # 等待成交
        time.sleep(3)
        
        # STEP_004: 验证平仓结果
        after_position = service.query_investor_position(instrument_id="IF2501")
        assert after_position.get('success'), "平仓后持仓查询失败"
        
        after_long_volume = 0
        for pos in after_position.get('data', []):
            if pos.get('InstrumentID') == 'IF2501' and pos.get('PosiDirection') == '2':  # 多头
                after_long_volume = pos.get('Position', 0)
                break
        
        # 验证持仓减少
        assert after_long_volume == before_long_volume - 1, f"持仓应减少1手，实际从{before_long_volume}变为{after_long_volume}"
        
        # 验证保证金释放
        after_account = service.query_trading_account()
        after_account_data = after_account.get('data', [{}])[0]
        after_available = after_account_data.get('Available', 0)
        margin_released = after_available - before_available
        assert margin_released > 0, "应该有保证金释放"
        
        # 验证盈亏结算
        trades = service.query_trade()
        assert trades.get('success'), "成交查询失败"
        
        print(f"✓ 平仓成功，持仓减少到{after_long_volume}手，保证金释放{margin_released}")
    
    def test_tc_fu004_insufficient_margin_restriction(self, ctp_service):
        """TC_FU004: 验证保证金不足时的开仓限制"""
        service = ctp_service
        
        # STEP_001: 查询当前资金状况
        account_result = service.query_trading_account()
        assert account_result.get('success'), "账户查询失败"
        
        account_data_list = account_result.get('data', [])
        account_data = account_data_list[0] if account_data_list else {}
        available_fund = account_data.get('Available', 0)  # 使用正确字段名
        print(f"✓ 当前可用资金: {available_fund}")
        
        # STEP_002: 计算大额开仓保证金需求
        large_volume = 10
        contract_price = 4000.0
        required_margin = contract_price * 300 * large_volume * 0.12  # 约480万
        
        print(f"✓ 开仓{large_volume}手需要保证金: {required_margin}")
        
        # STEP_003: 如果资金充足，先消耗资金到不足状态
        if available_fund >= required_margin:
            # 通过开仓消耗资金
            consume_volume = int(available_fund / (contract_price * 300 * 0.12)) - 1
            if consume_volume > 0:
                service.insert_order_direct(
                    instrument_id="IF2501",
                    direction="买",
                    offset_flag="开仓",
                    price=contract_price,
                    volume=min(consume_volume, 5)  # 限制最大消耗量
                )
                time.sleep(2)
        
        # STEP_004: 尝试大额开仓
        large_order_result = service.insert_order_direct(
            instrument_id="IF2501",
            exchange_id="CFFEX",
            direction="买",
            offset_flag="开仓",
            price_type="LimitPrice",
            limit_price=4000.0,
            volume=large_volume,
            hedge_flag="投机"
        )
        
        # STEP_005: 验证风控提示
        # 检查订单是否被拒绝或查询订单状态
        time.sleep(2)
        orders = service.query_order()
        
        if orders.get('success') and orders.get('data'):
            latest_order = orders['data'][-1]  # 最新订单
            order_status = latest_order.get('order_status', '')
            
            # 验证订单被拒绝或资金不足
            if order_status in ['已拒绝', '错误']:
                print(f"✓ 大额开仓被正确拒绝，订单状态: {order_status}")
            else:
                # 检查是否因资金不足未能完全成交
                filled_volume = latest_order.get('volume_traded', 0)
                if filled_volume < large_volume:
                    print(f"✓ 大额开仓部分成交，成交{filled_volume}手，剩余{large_volume-filled_volume}手因资金不足")
        
        # 验证账户状态
        final_account = service.query_trading_account()
        final_available = final_account.get('data', {}).get('available', 0)
        print(f"✓ 最终可用资金: {final_available}")
    
    def test_tc_fu005_commodity_futures_open(self, ctp_service):
        """TC_FU005: 验证商品期货开仓功能"""
        service = ctp_service
        
        # STEP_001: 查询账户资金
        account_result = service.query_trading_account()
        assert account_result.get('success'), "账户查询失败"
        
        account_data_list = account_result.get('data', [])
        account_data = account_data_list[0] if account_data_list else {}
        available_fund = account_data.get('Available', 0)  # 使用正确字段名
        assert available_fund >= 20000, f"可用资金{available_fund}不足20000"
        print(f"✓ 账户可用资金: {available_fund}")
        
        # STEP_002: 查询螺纹钢合约
        instrument_result = service.query_instrument(
            instrument_id="rb2501",
            exchange_id="SHFE"
        )
        assert instrument_result.get('success'), "螺纹钢合约查询失败"
        
        # STEP_003: 计算商品期货保证金
        volume = 10
        contract_price = 3500.0
        estimated_margin = contract_price * 10 * volume * 0.08  # rb合约乘数10，保证金率约8%
        assert available_fund >= estimated_margin, f"保证金不足，需要{estimated_margin}"
        print(f"✓ 预估保证金: {estimated_margin}")
        
        # STEP_004: 提交商品期货开仓
        before_position = service.query_investor_position(instrument_id="rb2501")
        before_volume = 0
        if before_position.get('success'):
            for pos in before_position.get('data', []):
                if pos.get('InstrumentID') == 'rb2501' and pos.get('PosiDirection') == '2':
                    before_volume = pos.get('Position', 0)
                    break
        
        order_result = service.insert_order_direct(
            instrument_id="rb2501",
            exchange_id="SHFE",
            direction="买",
            offset_flag="开仓",
            price_type="LimitPrice",
            limit_price=3500.0,
            volume=volume,
            hedge_flag="投机"
        )
        assert order_result is not None, "商品期货订单提交失败"
        print(f"✓ 螺纹钢买入开仓{volume}手订单提交成功")
        
        # 等待成交
        time.sleep(3)
        
        # STEP_005: 验证商品期货开仓
        after_position = service.query_investor_position(instrument_id="rb2501")
        assert after_position.get('success'), "开仓后持仓查询失败"
        
        after_volume = 0
        for pos in after_position.get('data', []):
            if pos.get('InstrumentID') == 'rb2501' and pos.get('PosiDirection') == '2':
                after_volume = pos.get('Position', 0)
                break
        
        # 验证持仓增加
        position_increase = after_volume - before_volume
        assert position_increase > 0, f"持仓应该增加，实际从{before_volume}变为{after_volume}"
        
        # 验证保证金计算正确
        after_account = service.query_trading_account()
        margin_used = available_fund - after_account.get('data', {}).get('available', 0)
        
        print(f"✓ 商品期货开仓成功，持仓增加{position_increase}手，保证金占用{margin_used}")
    
    def test_tc_fu101_margin_sufficiency_verification(self, ctp_service):
        """TC_FU101: 验证开仓保证金充足性"""
        service = ctp_service
        
        # STEP_001: 查询保证金状态
        account_result = service.query_trading_account()
        assert account_result.get('success'), "账户查询失败"
        
        account_data = account_result.get('data', {})
        available_fund = account_data.get('available', 0)
        margin_used = account_data.get('curr_margin', 0)
        
        print(f"✓ 可用资金: {available_fund}, 已用保证金: {margin_used}")
        
        # STEP_002: 计算所需保证金
        volume = 2
        contract_price = 4000.0
        required_margin = contract_price * 300 * volume * 0.12  # 约96万
        
        print(f"✓ 开仓{volume}手需要保证金: {required_margin}")
        
        # STEP_003: 判断保证金是否充足
        if available_fund >= required_margin:
            # 资金充足，应该能开仓
            order_result = service.insert_order_direct(
                instrument_id="IF2501",
                direction="买",
                offset_flag="开仓",
                price=contract_price,
                volume=volume
            )
            assert order_result is not None, "资金充足时开仓应该成功"
            print(f"✓ 保证金充足，开仓成功")
            
        else:
            # 资金不足，应该被拒绝
            order_result = service.insert_order_direct(
                instrument_id="IF2501",
                direction="买",
                offset_flag="开仓",
                price=contract_price,
                volume=volume
            )
            
            # 检查订单状态
            time.sleep(2)
            orders = service.query_order()
            if orders.get('success') and orders.get('data'):
                latest_order = orders['data'][-1]
                order_status = latest_order.get('order_status', '')
                print(f"✓ 保证金不足，订单状态: {order_status}")
    
    def test_tc_fu102_maintenance_margin_monitoring(self, ctp_service):
        """TC_FU102: 验证维持保证金监控"""
        service = ctp_service
        
        # STEP_001: 建立测试持仓
        self._ensure_long_position(service, "IF2501", 1)
        
        # STEP_002: 查询持仓保证金
        position_result = service.query_investor_position(instrument_id="IF2501")
        assert position_result.get('success'), "持仓查询失败"
        
        position_margin = 0
        for pos in position_result.get('data', []):
            if pos.get('instrument_id') == 'IF2501':
                position_margin = pos.get('use_margin', 0)
                break
        
        assert position_margin > 0, "应该有保证金占用"
        print(f"✓ 持仓保证金占用: {position_margin}")
        
        # STEP_003: 查询账户保证金状态
        account_result = service.query_trading_account()
        account_data = account_result.get('data', {})
        
        total_margin = account_data.get('curr_margin', 0)
        available_fund = account_data.get('available', 0)
        total_asset = account_data.get('balance', 0)
        
        # STEP_004: 计算维持保证金比率
        if total_asset > 0:
            margin_ratio = total_margin / total_asset
            print(f"✓ 保证金比率: {margin_ratio:.2%}")
            
            # 检查是否接近维持保证金要求
            if margin_ratio > 0.8:  # 假设80%为警戒线
                print(f"⚠ 保证金比率较高，接近风控线")
            else:
                print(f"✓ 保证金比率正常")
        
        # STEP_005: 模拟价格变动影响（通过查询浮动盈亏）
        position_detail = service.query_investor_position_detail(instrument_id="IF2501")
        if position_detail.get('success') and position_detail.get('data'):
            for detail in position_detail['data']:
                unrealized_pnl = detail.get('position_profit', 0)
                print(f"✓ 持仓浮动盈亏: {unrealized_pnl}")
                
                if unrealized_pnl < -position_margin * 0.5:  # 亏损超过保证金50%
                    print(f"⚠ 浮动亏损较大，可能触发风控")
    
    def _ensure_long_position(self, service, instrument_id, volume):
        """确保有指定的多头持仓"""
        position_result = service.query_investor_position(instrument_id=instrument_id)
        
        current_volume = 0
        if position_result.get('success'):
            for pos in position_result.get('data', []):
                if pos.get('InstrumentID') == instrument_id and pos.get('PosiDirection') == '2':
                    current_volume = pos.get('Position', 0)
                    break
        
        if current_volume < volume:
            need_volume = volume - current_volume
            service.insert_order_direct(
                instrument_id=instrument_id,
                direction="买",
                offset_flag="开仓",
                price=4000.0,
                volume=need_volume
            )
            time.sleep(3)  # 等待成交
    
    @pytest.mark.parametrize("instrument_id,exchange_id,price,volume", [
        ("IF2501", "CFFEX", 4000.0, 1),
        ("rb2501", "SHFE", 3500.0, 5),
        ("cu2501", "SHFE", 75000.0, 1),
    ])
    def test_multiple_contracts_trading(self, ctp_service, instrument_id, exchange_id, price, volume):
        """测试多个合约的交易功能"""
        service = ctp_service
        
        # 查询合约信息
        instrument_result = service.query_instrument(
            instrument_id=instrument_id,
            exchange_id=exchange_id
        )
        
        if not instrument_result.get('success'):
            pytest.skip(f"合约{instrument_id}查询失败，跳过测试")
        
        # 查询账户资金
        account_result = service.query_trading_account()
        available_fund = account_result.get('data', {}).get('available', 0)
        
        # 估算保证金需求
        if instrument_id.startswith('IF'):
            estimated_margin = price * 300 * volume * 0.12
        elif instrument_id.startswith('rb'):
            estimated_margin = price * 10 * volume * 0.08
        elif instrument_id.startswith('cu'):
            estimated_margin = price * 5 * volume * 0.08
        else:
            estimated_margin = price * volume * 0.1
        
        if available_fund < estimated_margin:
            pytest.skip(f"资金不足，需要{estimated_margin}，可用{available_fund}")
        
        # 执行开仓
        order_result = service.insert_order_direct(
            instrument_id=instrument_id,
            exchange_id=exchange_id,
            direction="买",
            offset_flag="开仓",
            price=price,
            volume=volume
        )
        
        assert order_result is not None, f"{instrument_id}开仓订单提交失败"
        print(f"✓ {instrument_id}开仓{volume}手订单提交成功")
        
        # 验证结果
        time.sleep(3)
        position_result = service.query_investor_position(instrument_id=instrument_id)
        assert position_result.get('success'), f"{instrument_id}持仓查询失败"
        
        print(f"✓ {instrument_id}交易测试完成")
