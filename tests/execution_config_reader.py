#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行配置读取器
读取测试执行配置文件
"""

import os
import yaml
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class ExecutionConfig:
    """执行配置"""
    mode: str
    target: str
    auto_cleanup: bool
    generate_report: bool
    save_json: bool

@dataclass
class EnvironmentConfig:
    """环境配置"""
    ctp_config: Dict[str, Any]
    system: Dict[str, Any]
    directories: Dict[str, str]

@dataclass
class TestDataConfig:
    """测试数据配置"""
    account: Dict[str, float]
    instruments: Dict[str, Dict[str, Any]]

@dataclass
class ReportConfig:
    """报告配置"""
    title: str
    include_environment: bool
    include_step_details: bool
    include_trading_config: bool
    include_messages: bool
    include_execution_check: bool
    output: Dict[str, Any]

class ExecutionConfigReader:
    """执行配置读取器"""
    
    def __init__(self, config_path: str = "config/test_execution_config.yaml"):
        self.config_path = config_path
        self.config_data = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f)
        except Exception as e:
            raise RuntimeError(f"加载配置文件失败: {e}")
    
    def get_execution_config(self) -> ExecutionConfig:
        """获取执行配置"""
        exec_config = self.config_data.get('execution', {})
        return ExecutionConfig(
            mode=exec_config.get('mode', 'scenario'),
            target=exec_config.get('target', ''),
            auto_cleanup=exec_config.get('auto_cleanup', True),
            generate_report=exec_config.get('generate_report', True),
            save_json=exec_config.get('save_json', True)
        )
    
    def get_environment_config(self) -> EnvironmentConfig:
        """获取环境配置"""
        env_config = self.config_data.get('environment', {})
        return EnvironmentConfig(
            ctp_config=env_config.get('ctp_config', {}),
            system=env_config.get('system', {}),
            directories=env_config.get('directories', {})
        )
    
    def get_test_data_config(self) -> TestDataConfig:
        """获取测试数据配置"""
        test_data = self.config_data.get('test_data', {})
        return TestDataConfig(
            account=test_data.get('account', {}),
            instruments=test_data.get('instruments', {})
        )
    
    def get_report_config(self) -> ReportConfig:
        """获取报告配置"""
        report_config = self.config_data.get('report', {})
        return ReportConfig(
            title=report_config.get('title', 'CTP期货交易自动化测试报告'),
            include_environment=report_config.get('include_environment', True),
            include_step_details=report_config.get('include_step_details', True),
            include_trading_config=report_config.get('include_trading_config', True),
            include_messages=report_config.get('include_messages', True),
            include_execution_check=report_config.get('include_execution_check', True),
            output=report_config.get('output', {})
        )
    
    def get_execution_order(self) -> List[str]:
        """获取执行顺序"""
        return self.config_data.get('execution_order', [])
    
    def get_instrument_config(self, instrument_id: str) -> Optional[Dict[str, Any]]:
        """获取合约配置"""
        test_data = self.get_test_data_config()
        return test_data.instruments.get(instrument_id)
    
    def get_account_config(self) -> Dict[str, float]:
        """获取账户配置"""
        test_data = self.get_test_data_config()
        return test_data.account
    
    def update_config(self, section: str, key: str, value: Any):
        """更新配置"""
        if section not in self.config_data:
            self.config_data[section] = {}
        self.config_data[section][key] = value
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config_data, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            raise RuntimeError(f"保存配置文件失败: {e}")


def main():
    """主函数"""
    print("执行配置读取器测试")
    print("=" * 50)
    
    try:
        reader = ExecutionConfigReader()
        
        # 显示配置信息
        exec_config = reader.get_execution_config()
        env_config = reader.get_environment_config()
        test_data_config = reader.get_test_data_config()
        report_config = reader.get_report_config()
        
        print(f"执行配置:")
        print(f"  模式: {exec_config.mode}")
        print(f"  目标: {exec_config.target}")
        print(f"  自动清理: {exec_config.auto_cleanup}")
        print(f"  生成报告: {exec_config.generate_report}")
        
        print(f"\n环境配置:")
        print(f"  交易前置机: {env_config.ctp_config.get('trade_front', 'N/A')}")
        print(f"  行情前置机: {env_config.ctp_config.get('md_front', 'N/A')}")
        print(f"  经纪商ID: {env_config.ctp_config.get('broker_id', 'N/A')}")
        print(f"  用户ID: {env_config.ctp_config.get('user_id', 'N/A')}")
        print(f"  投资者ID: {env_config.ctp_config.get('investor_id', 'N/A')}")
        print(f"  超时时间: {env_config.system.get('timeout', 'N/A')}秒")
        print(f"  重试次数: {env_config.system.get('retry_count', 'N/A')}")
        print(f"  日志级别: {env_config.system.get('log_level', 'N/A')}")

        print(f"\n目录配置:")
        for dir_name, dir_path in env_config.directories.items():
            print(f"  {dir_name}: {dir_path}")
        
        print(f"\n测试数据配置:")
        print(f"  初始资金: {test_data_config.account.get('initial_fund', 0):.2f}")
        print(f"  可用资金: {test_data_config.account.get('available_fund', 0):.2f}")
        print(f"  合约数量: {len(test_data_config.instruments)}")
        
        print(f"\n报告配置:")
        print(f"  标题: {report_config.title}")
        print(f"  包含环境信息: {report_config.include_environment}")
        print(f"  包含步骤详情: {report_config.include_step_details}")
        print(f"  包含交易配置: {report_config.include_trading_config}")
        
        execution_order = reader.get_execution_order()
        print(f"\n执行顺序 ({len(execution_order)} 个场景):")
        for i, scenario_id in enumerate(execution_order, 1):
            print(f"  {i}. {scenario_id}")
        
    except Exception as e:
        print(f"配置读取失败: {e}")


if __name__ == "__main__":
    main()
