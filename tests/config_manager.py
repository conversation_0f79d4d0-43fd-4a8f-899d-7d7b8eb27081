#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
分为环境配置和交易配置两个层次
"""

import os
import yaml
from typing import Dict, Any, Optional
from dataclasses import dataclass
from execution_config_reader import ExecutionConfigReader

@dataclass
class EnvironmentConfig:
    """环境配置"""
    # CTP连接配置
    trade_front: str
    md_front: str
    broker_id: str
    user_id: str
    password: str
    investor_id: str
    
    # 系统配置
    timeout: int = 30
    retry_count: int = 3
    log_level: str = "INFO"
    
    # 目录配置
    data_dir: str = "./data"
    result_dir: str = "./result"
    plot_dir: str = "./plot"
    config_dir: str = "./config"

@dataclass
class TradingConfig:
    """交易配置"""
    # 合约信息
    instrument_id: str
    exchange_id: str
    
    # 交易参数
    direction: str  # Buy/Sell
    offset_flag: str  # Open/Close/CloseToday/CloseYesterday
    hedge_flag: str  # Speculation/Arbitrage/Hedge
    price_type: str  # LimitPrice/MarketPrice/BestPrice
    
    # 价格和数量
    limit_price: float
    volume: int
    
    # 风控参数
    max_position: int = 100
    max_order_volume: int = 10
    price_deviation_limit: float = 0.05  # 5%

class ConfigManager:
    """配置管理器"""

    def __init__(self, use_execution_config: bool = True):
        self.use_execution_config = use_execution_config
        self.env_config: Optional[EnvironmentConfig] = None
        self.trading_config: Optional[TradingConfig] = None
        self.original_configs = {}

        if use_execution_config:
            # 从test_execution_config.yaml读取配置
            self.exec_config_reader = ExecutionConfigReader()
            self._load_from_execution_config()
        else:
            # 从传统的environment.yaml读取配置
            self.env_config_path = "config/environment.yaml"
            self._load_environment_config()

        self._ensure_directories()

    def _load_from_execution_config(self):
        """从test_execution_config.yaml加载环境配置"""
        try:
            env_config = self.exec_config_reader.get_environment_config()

            # 转换为EnvironmentConfig对象
            self.env_config = EnvironmentConfig(
                trade_front=env_config.ctp_config.get('trade_front', ''),
                md_front=env_config.ctp_config.get('md_front', ''),
                broker_id=env_config.ctp_config.get('broker_id', ''),
                user_id=env_config.ctp_config.get('user_id', ''),
                password=env_config.ctp_config.get('password', ''),
                investor_id=env_config.ctp_config.get('investor_id', ''),
                timeout=env_config.system.get('timeout', 30),
                retry_count=env_config.system.get('retry_count', 3),
                log_level=env_config.system.get('log_level', 'INFO'),
                data_dir=env_config.directories.get('data', './data'),
                result_dir=env_config.directories.get('result', './result'),
                plot_dir=env_config.directories.get('plot', './plot'),
                config_dir=env_config.directories.get('config', './config')
            )

            print(f"✓ 从test_execution_config.yaml加载环境配置成功")

        except Exception as e:
            raise RuntimeError(f"从执行配置加载环境配置失败: {e}")

    def _load_environment_config(self):
        """加载环境配置"""
        if not os.path.exists(self.env_config_path):
            raise FileNotFoundError(f"环境配置文件不存在: {self.env_config_path}")

        try:
            with open(self.env_config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            if not config_data:
                raise ValueError("环境配置文件为空")

            # 检查必需的配置项
            required_sections = ['ctp', 'system', 'directories']
            for section in required_sections:
                if section not in config_data:
                    raise KeyError(f"缺少必需的配置节: {section}")

            # 检查CTP配置
            ctp_config = config_data['ctp']
            required_ctp_fields = ['trade_front', 'md_front', 'broker_id', 'user_id', 'password', 'investor_id']
            for field in required_ctp_fields:
                if field not in ctp_config:
                    raise KeyError(f"缺少必需的CTP配置项: {field}")
                if not ctp_config[field] or str(ctp_config[field]).strip() == '':
                    raise ValueError(f"CTP配置项不能为空: {field}")

            # 检查系统配置
            system_config = config_data['system']
            required_system_fields = ['timeout', 'retry_count', 'log_level']
            for field in required_system_fields:
                if field not in system_config:
                    raise KeyError(f"缺少必需的系统配置项: {field}")

            # 检查目录配置
            dir_config = config_data['directories']
            required_dir_fields = ['data', 'result', 'plot', 'config']
            for field in required_dir_fields:
                if field not in dir_config:
                    raise KeyError(f"缺少必需的目录配置项: {field}")

            self.env_config = EnvironmentConfig(
                trade_front=ctp_config['trade_front'],
                md_front=ctp_config['md_front'],
                broker_id=ctp_config['broker_id'],
                user_id=ctp_config['user_id'],
                password=ctp_config['password'],
                investor_id=ctp_config['investor_id'],
                timeout=int(system_config['timeout']),
                retry_count=int(system_config['retry_count']),
                log_level=system_config['log_level'],
                data_dir=dir_config['data'],
                result_dir=dir_config['result'],
                plot_dir=dir_config['plot'],
                config_dir=dir_config['config']
            )

        except Exception as e:
            raise RuntimeError(f"加载环境配置失败: {e}")
    
    def _save_environment_config(self):
        """保存环境配置"""
        if not self.env_config:
            return
        
        config_data = {
            'ctp': {
                'trade_front': self.env_config.trade_front,
                'md_front': self.env_config.md_front,
                'broker_id': self.env_config.broker_id,
                'user_id': self.env_config.user_id,
                'password': self.env_config.password,
                'investor_id': self.env_config.investor_id
            },
            'system': {
                'timeout': self.env_config.timeout,
                'retry_count': self.env_config.retry_count,
                'log_level': self.env_config.log_level
            },
            'directories': {
                'data': self.env_config.data_dir,
                'result': self.env_config.result_dir,
                'plot': self.env_config.plot_dir,
                'config': self.env_config.config_dir
            }
        }
        
        os.makedirs(os.path.dirname(self.env_config_path), exist_ok=True)
        with open(self.env_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, indent=2, allow_unicode=True)
    
    def _ensure_directories(self):
        """确保必要目录存在"""
        if self.env_config:
            for directory in [self.env_config.data_dir, self.env_config.result_dir, 
                            self.env_config.plot_dir, self.env_config.config_dir]:
                os.makedirs(directory, exist_ok=True)
    
    def create_trading_config(self, **kwargs) -> TradingConfig:
        """创建交易配置"""
        # 检查必需的交易配置参数
        required_fields = ['instrument_id', 'exchange_id', 'direction', 'offset_flag',
                          'hedge_flag', 'price_type', 'limit_price', 'volume']

        for field in required_fields:
            if field not in kwargs:
                raise KeyError(f"缺少必需的交易配置参数: {field}")
            if kwargs[field] is None or kwargs[field] == '':
                raise ValueError(f"交易配置参数不能为空: {field}")

        # 验证数值类型参数
        try:
            limit_price = float(kwargs['limit_price'])
            volume = int(kwargs['volume'])
            if limit_price <= 0:
                raise ValueError("limit_price必须大于0")
            if volume <= 0:
                raise ValueError("volume必须大于0")
        except (ValueError, TypeError) as e:
            raise ValueError(f"交易配置参数类型错误: {e}")

        self.trading_config = TradingConfig(
            instrument_id=kwargs['instrument_id'],
            exchange_id=kwargs['exchange_id'],
            direction=kwargs['direction'],
            offset_flag=kwargs['offset_flag'],
            hedge_flag=kwargs['hedge_flag'],
            price_type=kwargs['price_type'],
            limit_price=limit_price,
            volume=volume,
            max_position=kwargs.get('max_position', 100),
            max_order_volume=kwargs.get('max_order_volume', 10),
            price_deviation_limit=kwargs.get('price_deviation_limit', 0.05)
        )
        return self.trading_config
    
    def update_ctp_config_file(self, target_config_path: str = "config/config.yaml") -> bool:
        """更新CTP程序的配置文件"""
        try:
            # 备份原始配置
            if os.path.exists(target_config_path) and target_config_path not in self.original_configs:
                with open(target_config_path, 'r', encoding='utf-8') as f:
                    self.original_configs[target_config_path] = yaml.safe_load(f)
            
            # 准备新配置
            config_data = {}
            
            # 环境配置部分
            if self.env_config:
                config_data['ctp'] = {
                    'trade_front': self.env_config.trade_front,
                    'md_front': self.env_config.md_front,
                    'broker_id': self.env_config.broker_id,
                    'user_id': self.env_config.user_id,
                    'password': self.env_config.password,
                    'investor_id': self.env_config.investor_id
                }
                
                config_data['system'] = {
                    'timeout': self.env_config.timeout,
                    'retry_count': self.env_config.retry_count,
                    'log_level': self.env_config.log_level
                }
            
            # 交易配置部分
            if self.trading_config:
                config_data['trading'] = {
                    'instrument_id': self.trading_config.instrument_id,
                    'exchange_id': self.trading_config.exchange_id,
                    'direction': self.trading_config.direction,
                    'offset_flag': self.trading_config.offset_flag,
                    'hedge_flag': self.trading_config.hedge_flag,
                    'price_type': self.trading_config.price_type,
                    'limit_price': self.trading_config.limit_price,
                    'volume': self.trading_config.volume
                }
                
                config_data['risk_control'] = {
                    'max_position': self.trading_config.max_position,
                    'max_order_volume': self.trading_config.max_order_volume,
                    'price_deviation_limit': self.trading_config.price_deviation_limit
                }
            
            # 保存配置文件
            os.makedirs(os.path.dirname(target_config_path), exist_ok=True)
            with open(target_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, indent=2, allow_unicode=True)
            
            return True
            
        except Exception as e:
            print(f"更新CTP配置文件失败: {e}")
            return False
    
    def restore_original_config(self, target_config_path: str = "config/config.yaml") -> bool:
        """恢复原始配置"""
        try:
            if target_config_path in self.original_configs:
                with open(target_config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(self.original_configs[target_config_path], 
                            f, default_flow_style=False, indent=2, allow_unicode=True)
                return True
            return False
        except Exception as e:
            print(f"恢复原始配置失败: {e}")
            return False
    
    def get_environment_config(self) -> Optional[EnvironmentConfig]:
        """获取环境配置"""
        return self.env_config
    
    def get_trading_config(self) -> Optional[TradingConfig]:
        """获取交易配置"""
        return self.trading_config
    
    def print_current_config(self):
        """打印当前配置"""
        print("当前配置信息:")
        print("=" * 50)
        
        if self.env_config:
            print("环境配置:")
            print(f"  交易前置: {self.env_config.trade_front}")
            print(f"  行情前置: {self.env_config.md_front}")
            print(f"  经纪商ID: {self.env_config.broker_id}")
            print(f"  用户ID: {self.env_config.user_id}")
            print(f"  投资者ID: {self.env_config.investor_id}")
            print(f"  超时时间: {self.env_config.timeout}秒")
            print(f"  重试次数: {self.env_config.retry_count}")
            print(f"  日志级别: {self.env_config.log_level}")
        
        if self.trading_config:
            print("\n交易配置:")
            print(f"  合约代码: {self.trading_config.instrument_id}")
            print(f"  交易所: {self.trading_config.exchange_id}")
            print(f"  方向: {self.trading_config.direction}")
            print(f"  开平标志: {self.trading_config.offset_flag}")
            print(f"  投机套保: {self.trading_config.hedge_flag}")
            print(f"  价格类型: {self.trading_config.price_type}")
            print(f"  限价: {self.trading_config.limit_price}")
            print(f"  数量: {self.trading_config.volume}")
            print(f"  最大持仓: {self.trading_config.max_position}")
            print(f"  最大单笔: {self.trading_config.max_order_volume}")





def main():
    """主函数"""
    print("配置管理器测试")
    print("=" * 50)

    try:
        # 创建配置管理器
        config_manager = ConfigManager()

        # 显示当前环境配置
        config_manager.print_current_config()

        # 创建测试交易配置
        print(f"\n创建测试交易配置...")
        config_manager.create_trading_config(
            instrument_id='IF2501',
            exchange_id='CFFEX',
            direction='Buy',
            offset_flag='Open',
            hedge_flag='Speculation',
            price_type='LimitPrice',
            limit_price=4000.0,
            volume=1
        )

        # 更新CTP配置文件
        print(f"\n更新CTP配置文件...")
        if config_manager.update_ctp_config_file():
            print("配置文件更新成功")

        # 显示更新后的配置
        config_manager.print_current_config()

        # 恢复原始配置
        print(f"\n恢复原始配置...")
        if config_manager.restore_original_config():
            print("原始配置恢复成功")
        else:
            print("没有原始配置可恢复")

    except Exception as e:
        print(f"配置管理器测试失败: {e}")
        print("请检查环境配置文件是否存在且格式正确")


if __name__ == "__main__":
    main()
