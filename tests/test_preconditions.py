#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前置条件测试
验证所有预设条件的检查功能
"""

import pytest
from datetime import datetime, time
from futures_config_reader import FuturesConfigReader
from futures_test_executor import FuturesTestExecutor


class TestPreconditions:
    """前置条件测试类"""
    
    @pytest.fixture(scope="class")
    def test_executor(self):
        """测试执行器fixture"""
        config_reader = FuturesConfigReader()
        return FuturesTestExecutor(config_reader)
    
    def test_precond_001_account_fund_sufficient(self, test_executor):
        """PRECOND_001: 账户资金充足"""
        result = test_executor.execute_precondition("PRECOND_001")
        assert isinstance(result, bool), "资金检查应返回布尔值"
        print(f"账户资金充足检查: {'通过' if result else '失败'}")
    
    def test_precond_002_futures_trading_permission(self, test_executor):
        """PRECOND_002: 具有期货交易权限"""
        result = test_executor.execute_precondition("PRECOND_002")
        assert isinstance(result, bool), "权限检查应返回布尔值"
        print(f"期货交易权限检查: {'通过' if result else '失败'}")
    
    def test_precond_003_trading_time(self, test_executor):
        """PRECOND_003: 在交易时间内"""
        result = test_executor.execute_precondition("PRECOND_003")
        assert isinstance(result, bool), "交易时间检查应返回布尔值"
        
        # 验证时间逻辑
        current_time = datetime.now().time()
        current_weekday = datetime.now().weekday()
        
        expected_result = False
        if current_weekday < 5:  # 工作日
            trading_sessions = [
                (time(9, 0), time(11, 30)),
                (time(13, 30), time(15, 0)),
                (time(21, 0), time(23, 0))
            ]
            for start_time, end_time in trading_sessions:
                if start_time <= current_time <= end_time:
                    expected_result = True
                    break
        
        print(f"交易时间检查: {'通过' if result else '失败'} (当前时间: {current_time})")
        assert result == expected_result, f"交易时间判断错误"
    
    def test_precond_004_contract_trading_status(self, test_executor):
        """PRECOND_004: 合约处于正常交易状态"""
        result = test_executor.execute_precondition("PRECOND_004")
        assert isinstance(result, bool), "合约状态检查应返回布尔值"
        print(f"IF2501合约交易状态检查: {'通过' if result else '失败'}")
    
    def test_precond_005_system_normal(self, test_executor):
        """PRECOND_005: 系统正常运行"""
        result = test_executor.execute_precondition("PRECOND_005")
        assert isinstance(result, bool), "系统状态检查应返回布尔值"
        print(f"系统状态检查: {'通过' if result else '失败'}")
    
    def test_precond_006_risk_config_correct(self, test_executor):
        """PRECOND_006: 风控参数配置正确"""
        result = test_executor.execute_precondition("PRECOND_006")
        assert isinstance(result, bool), "风控配置检查应返回布尔值"
        print(f"风控参数配置检查: {'通过' if result else '失败'}")
    
    def test_precond_007_price_data_normal(self, test_executor):
        """PRECOND_007: 价格数据正常"""
        result = test_executor.execute_precondition("PRECOND_007")
        assert isinstance(result, bool), "价格数据检查应返回布尔值"
        print(f"IF2501价格数据检查: {'通过' if result else '失败'}")
    
    def test_precond_008_set_low_fund(self, test_executor):
        """PRECOND_008: 设置较低的可用资金"""
        result = test_executor.execute_precondition("PRECOND_008")
        assert isinstance(result, bool), "资金设置应返回布尔值"
        print(f"设置低资金检查: {'通过' if result else '失败'}")
    
    def test_precond_009_account_status_normal(self, test_executor):
        """PRECOND_009: 账户状态正常"""
        result = test_executor.execute_precondition("PRECOND_009")
        assert isinstance(result, bool), "账户状态检查应返回布尔值"
        print(f"账户状态检查: {'通过' if result else '失败'}")
    
    def test_precond_010_query_permission(self, test_executor):
        """PRECOND_010: 具有查询权限"""
        result = test_executor.execute_precondition("PRECOND_010")
        assert isinstance(result, bool), "查询权限检查应返回布尔值"
        print(f"查询权限检查: {'通过' if result else '失败'}")
    
    def test_precond_011_stock_index_futures_permission(self, test_executor):
        """PRECOND_011: 具有股指期货权限"""
        result = test_executor.execute_precondition("PRECOND_011")
        assert isinstance(result, bool), "股指期货权限检查应返回布尔值"
        print(f"股指期货权限检查: {'通过' if result else '失败'}")
    
    def test_precond_012_commodity_futures_permission(self, test_executor):
        """PRECOND_012: 具有商品期货权限"""
        result = test_executor.execute_precondition("PRECOND_012")
        assert isinstance(result, bool), "商品期货权限检查应返回布尔值"
        print(f"商品期货权限检查: {'通过' if result else '失败'}")
    
    def test_precond_013_rebar_contract_normal(self, test_executor):
        """PRECOND_013: 螺纹钢合约正常"""
        result = test_executor.execute_precondition("PRECOND_013")
        assert isinstance(result, bool), "螺纹钢合约检查应返回布尔值"
        print(f"螺纹钢rb2501合约检查: {'通过' if result else '失败'}")
    
    def test_precond_014_position_exists(self, test_executor):
        """PRECOND_014: 持仓存在"""
        result = test_executor.execute_precondition("PRECOND_014")
        assert isinstance(result, bool), "持仓检查应返回布尔值"
        print(f"IF2501持仓检查: {'通过' if result else '失败'}")
    
    def test_precond_015_margin_monitor_normal(self, test_executor):
        """PRECOND_015: 保证金监控正常"""
        result = test_executor.execute_precondition("PRECOND_015")
        assert isinstance(result, bool), "保证金监控检查应返回布尔值"
        print(f"保证金监控检查: {'通过' if result else '失败'}")
    
    def test_precond_016_risk_system_active(self, test_executor):
        """PRECOND_016: 风控系统激活"""
        result = test_executor.execute_precondition("PRECOND_016")
        assert isinstance(result, bool), "风控系统检查应返回布尔值"
        print(f"风控系统激活检查: {'通过' if result else '失败'}")
    
    def test_precond_017_trading_auth_valid(self, test_executor):
        """PRECOND_017: 交易授权有效"""
        result = test_executor.execute_precondition("PRECOND_017")
        assert isinstance(result, bool), "交易授权检查应返回布尔值"
        print(f"交易授权检查: {'通过' if result else '失败'}")
    
    def test_precond_018_network_connected(self, test_executor):
        """PRECOND_018: 网络连接正常"""
        result = test_executor.execute_precondition("PRECOND_018")
        assert isinstance(result, bool), "网络连接检查应返回布尔值"
        print(f"网络连接检查: {'通过' if result else '失败'}")
    
    def test_precond_019_data_sync_normal(self, test_executor):
        """PRECOND_019: 数据同步正常"""
        result = test_executor.execute_precondition("PRECOND_019")
        assert isinstance(result, bool), "数据同步检查应返回布尔值"
        print(f"数据同步检查: {'通过' if result else '失败'}")
    
    def test_precond_020_settlement_system_normal(self, test_executor):
        """PRECOND_020: 清算系统正常"""
        result = test_executor.execute_precondition("PRECOND_020")
        assert isinstance(result, bool), "清算系统检查应返回布尔值"
        print(f"清算系统检查: {'通过' if result else '失败'}")
    
    @pytest.mark.parametrize("condition_id", [
        "PRECOND_001", "PRECOND_002", "PRECOND_003", "PRECOND_004", "PRECOND_005",
        "PRECOND_006", "PRECOND_007", "PRECOND_008", "PRECOND_009", "PRECOND_010",
        "PRECOND_011", "PRECOND_012", "PRECOND_013", "PRECOND_014", "PRECOND_015",
        "PRECOND_016", "PRECOND_017", "PRECOND_018", "PRECOND_019", "PRECOND_020"
    ])
    def test_all_preconditions_parametrized(self, test_executor, condition_id):
        """参数化测试所有前置条件"""
        precondition = test_executor.config_reader.get_precondition(condition_id)
        assert precondition is not None, f"前置条件 {condition_id} 不存在"
        
        result = test_executor.execute_precondition(condition_id)
        assert isinstance(result, bool), f"前置条件 {condition_id} 应返回布尔值"
        
        print(f"{condition_id} - {precondition.condition_name}: {'通过' if result else '失败'}")
    
    def test_precondition_retry_mechanism(self, test_executor):
        """测试前置条件重试机制"""
        # 测试一个可能失败的条件，验证重试逻辑
        condition_id = "PRECOND_007"  # 价格数据检查，可能因网络问题失败
        
        precondition = test_executor.config_reader.get_precondition(condition_id)
        assert precondition.retry_count == 3, "价格数据检查应该有3次重试"
        
        result = test_executor.execute_precondition(condition_id)
        print(f"重试机制测试 - {precondition.condition_name}: {'通过' if result else '失败'}")
    
    def test_precondition_error_messages(self, test_executor):
        """测试前置条件错误消息"""
        all_conditions = [
            "PRECOND_001", "PRECOND_002", "PRECOND_003", "PRECOND_004", "PRECOND_005",
            "PRECOND_006", "PRECOND_007", "PRECOND_008", "PRECOND_009", "PRECOND_010"
        ]
        
        for condition_id in all_conditions:
            precondition = test_executor.config_reader.get_precondition(condition_id)
            assert precondition.error_message, f"前置条件 {condition_id} 应该有错误消息"
            assert len(precondition.error_message) > 0, f"前置条件 {condition_id} 错误消息不能为空"
            print(f"{condition_id}: {precondition.error_message}")