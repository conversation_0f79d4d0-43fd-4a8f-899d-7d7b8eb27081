#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
条件判断功能测试
"""

import pytest
import sys
import os
from datetime import datetime, time

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from futures_test_executor import FuturesTestExecutor
from futures_config_reader import FuturesConfigReader

class TestConditionValidation:
    """条件判断功能测试"""
    
    @pytest.fixture(scope="class")
    def test_executor(self):
        """测试执行器fixture"""
        config_reader = FuturesConfigReader()
        return FuturesTestExecutor(config_reader)
    
    def test_account_condition_check(self, test_executor):
        """测试账户条件检查"""
        # 模拟预设条件
        from futures_config_reader import Precondition
        
        condition = Precondition(
            condition_id="TEST_ACCOUNT",
            condition_name="测试账户资金",
            condition_type="资金检查",
            check_method="query_account",
            check_params={'min_fund': 10000},
            expected_result="available_fund>=10000",
            error_message="资金不足",
            retry_count=1
        )
        
        result = test_executor._check_account_condition(condition)
        print(f"账户条件检查结果: {result}")
        
        # 验证返回布尔值
        assert isinstance(result, bool), "账户条件检查应返回布尔值"
    
    def test_trading_time_condition(self, test_executor):
        """测试交易时间条件检查"""
        from futures_config_reader import Precondition
        
        condition = Precondition(
            condition_id="TEST_TIME",
            condition_name="测试交易时间",
            condition_type="时间检查", 
            check_method="check_trading_time",
            check_params={},
            expected_result="is_trading_time=true",
            error_message="非交易时间",
            retry_count=1
        )
        
        result = test_executor._check_trading_time_condition(condition)
        print(f"交易时间检查结果: {result}")
        print(f"当前时间: {datetime.now()}")
        
        # 验证逻辑正确性
        current_time = datetime.now().time()
        current_weekday = datetime.now().weekday()
        
        expected_result = False
        if current_weekday < 5:  # 工作日
            trading_sessions = [
                (time(9, 0), time(11, 30)),
                (time(13, 30), time(15, 0)),
                (time(21, 0), time(23, 0))
            ]
            for start_time, end_time in trading_sessions:
                if start_time <= current_time <= end_time:
                    expected_result = True
                    break
        
        assert result == expected_result, f"交易时间判断错误，预期: {expected_result}, 实际: {result}"
    
    def test_system_status_condition(self, test_executor):
        """测试系统状态条件检查"""
        from futures_config_reader import Precondition
        
        condition = Precondition(
            condition_id="TEST_SYSTEM",
            condition_name="测试系统状态",
            condition_type="系统检查",
            check_method="check_system_status",
            check_params={},
            expected_result="system_status=normal",
            error_message="系统异常",
            retry_count=1
        )
        
        result = test_executor._check_system_status_condition(condition)
        print(f"系统状态检查结果: {result}")
        
        assert isinstance(result, bool), "系统状态检查应返回布尔值"
    
    def test_validation_rules(self, test_executor):
        """测试验证规则功能"""
        # 测试数值验证
        assert test_executor._validate_numeric(100, '>', 50) == True
        assert test_executor._validate_numeric(30, '>', 50) == False
        assert test_executor._validate_numeric(50, '=', 50) == True
        assert test_executor._validate_numeric(50, '>=', 50) == True
        
        # 测试字符串验证
        assert test_executor._validate_string("success", '=', "success") == True
        assert test_executor._validate_string("failed", '=', "success") == False
        assert test_executor._validate_string("test", '!=', "demo") == True
        
        # 测试布尔验证
        assert test_executor._validate_boolean(True, '=', True) == True
        assert test_executor._validate_boolean(False, '=', True) == False
        assert test_executor._validate_boolean(True, '!=', False) == True
        
        # 测试非空验证
        assert test_executor._validate_not_null("value", '!=', None) == True
        assert test_executor._validate_not_null(None, '!=', None) == False
        assert test_executor._validate_not_null("", '!=', None) == False
        
        # 测试范围验证
        assert test_executor._validate_range(75, 'between', "50,100") == True
        assert test_executor._validate_range(25, 'between', "50,100") == False
        assert test_executor._validate_range(25, 'not_between', "50,100") == True
        
        # 测试包含验证
        assert test_executor._validate_contains("hello world", 'contains', "world") == True
        assert test_executor._validate_contains("hello world", 'contains', "test") == False
        assert test_executor._validate_contains("hello world", 'not_contains', "test") == True
        
        print("✓ 所有验证规则测试通过")
    
    def test_nested_field_access(self, test_executor):
        """测试嵌套字段访问"""
        test_data = {
            "account": {
                "balance": 100000,
                "available": 80000,
                "margin": {
                    "used": 20000,
                    "available": 80000
                }
            },
            "status": "success"
        }
        
        # 测试简单字段
        assert test_executor._get_nested_field_value(test_data, "status") == "success"
        
        # 测试嵌套字段
        assert test_executor._get_nested_field_value(test_data, "account.balance") == 100000
        assert test_executor._get_nested_field_value(test_data, "account.margin.used") == 20000
        
        # 测试不存在的字段
        assert test_executor._get_nested_field_value(test_data, "account.nonexistent") is None
        assert test_executor._get_nested_field_value(test_data, "nonexistent.field") is None
        
        print("✓ 嵌套字段访问测试通过")
    
    def test_complete_validation_flow(self, test_executor):
        """测试完整验证流程"""
        # 模拟实际结果
        actual_result = {
            "order_status": "submitted",
            "available_fund": 75000,
            "margin_used": 25000,
            "account": {
                "balance": 100000,
                "available": 75000
            }
        }
        
        # 模拟验证规则
        validation_rules = ["RULE_001", "RULE_002"]
        
        # 由于需要实际的验证规则配置，这里主要测试流程
        validation_results = test_executor.validate_result(actual_result, validation_rules)
        
        assert isinstance(validation_results, list), "验证结果应为列表"
        
        for result in validation_results:
            assert 'rule_id' in result, "验证结果应包含rule_id"
            assert 'status' in result, "验证结果应包含status"
            assert result['status'] in ['通过', '失败', '跳过', '错误'], f"无效的验证状态: {result['status']}"
        
        print(f"✓ 完整验证流程测试通过，验证了 {len(validation_results)} 个规则")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])