#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易场景集成测试
"""

import time
import sys
import os
import pytest
from typing import List, Dict, Any
from automated_test_runner import AutomatedTestRunner

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from trader.ctp_direct_wrapper import CTPDirectWrapper

class TestTradingScenarios:
    """交易场景测试"""
    
    @pytest.fixture(scope="class")
    def trading_session(self):
        """交易会话fixture"""
        service = CTPDirectWrapper()
        
        # 初始化并等待连接
        init_result = service.initialize()
        assert init_result, "服务初始化失败"
        
        # 等待连接和登录完成
        max_wait = 30
        connected = False
        for _ in range(max_wait):
            if service.is_connected() and service.is_logged_in():
                connected = True
                break
            time.sleep(1)
        
        assert connected, "连接或登录失败"
        print(f"✓ 交易会话建立成功")
        
        yield service
        
        # 清理工作
        try:
            # 查询并清理测试持仓
            positions = service.query_investor_position()
            if positions.get('success'):
                for pos in positions.get('data', []):
                    if pos.get('long_volume', 0) > 0 or pos.get('short_volume', 0) > 0:
                        print(f"清理持仓: {pos['instrument_id']}")
        except:
            pass

    def test_complete_trading_flow_with_verification(self, trading_session):
        """完整交易流程测试 - 包含账户和持仓变化验证"""
        service = trading_session
        
        # 1. 查询初始状态
        initial_account = service.query_trading_account()
        assert initial_account.get('success'), "初始账户查询失败"
        
        account_data_list = initial_account.get('data', [])
        account_data = account_data_list[0] if account_data_list else {}
        initial_balance = account_data.get('Balance', 0)  # 使用正确字段名
        initial_available = account_data.get('Available', 0)  # 使用正确字段名
        
        initial_position = service.query_investor_position()
        assert initial_position.get('success'), "初始持仓查询失败"
        initial_positions = initial_position.get('data', [])
        
        # 获取IF2512的初始持仓
        initial_if_position = None
        for pos in initial_positions:
            if pos.get('InstrumentID') == 'IF2512':  # 使用正确字段名
                initial_if_position = pos
                break
        
        # 根据PosiDirection判断多空持仓
        initial_long_volume = 0
        initial_short_volume = 0
        if initial_if_position:
            direction = initial_if_position.get('PosiDirection', '1')
            volume = initial_if_position.get('Position', 0)
            if direction == '2':  # 多头
                initial_long_volume = volume
            elif direction == '3':  # 空头
                initial_short_volume = volume
        
        # 2. 执行买入开仓
        buy_order = {
            "instrument_id": "IF2512",
            "direction": "买",
            "offset_flag": "开仓",
            "price": 3500.0,
            "volume": 1
        }
        
        buy_result = service.insert_order_direct(**buy_order)
        assert buy_result is not None, "买入订单提交失败"
        
        # 等待订单处理
        time.sleep(3)
        
        # 3. 查询买入后状态
        after_buy_account = service.query_trading_account()
        assert after_buy_account.get('success'), "买入后账户查询失败"
        
        after_buy_position = service.query_investor_position()
        assert after_buy_position.get('success'), "买入后持仓查询失败"
        
        # 验证持仓变化
        after_buy_positions = after_buy_position.get('data', [])
        after_buy_if_position = None
        for pos in after_buy_positions:
            if pos.get('instrument_id') == 'IF2512':
                after_buy_if_position = pos
                break
        
        if after_buy_if_position:
            after_buy_long_volume = after_buy_if_position.get('long_volume', 0)
            # 验证多头持仓增加
            assert after_buy_long_volume == initial_long_volume + 1, f"多头持仓应增加1手，实际从{initial_long_volume}变为{after_buy_long_volume}"
        
        # 4. 执行卖出平仓
        sell_order = {
            "instrument_id": "IF2512", 
            "direction": "卖",
            "offset_flag": "平仓",
            "price": 3490.0,
            "volume": 1
        }
        
        sell_result = service.insert_order_direct(**sell_order)
        assert sell_result is not None, "卖出订单提交失败"
        
        # 等待订单处理
        time.sleep(3)
        
        # 5. 查询平仓后状态
        final_account = service.query_trading_account()
        assert final_account.get('success'), "最终账户查询失败"
        final_balance = final_account.get('data', {}).get('balance', 0)
        
        final_position = service.query_investor_position()
        assert final_position.get('success'), "最终持仓查询失败"
        
        # 验证最终持仓回到初始状态
        final_positions = final_position.get('data', [])
        final_if_position = None
        for pos in final_positions:
            if pos.get('instrument_id') == 'IF2512':
                final_if_position = pos
                break
        
        final_long_volume = final_if_position.get('long_volume', 0) if final_if_position else 0
        assert final_long_volume == initial_long_volume, f"最终多头持仓应回到初始值{initial_long_volume}，实际为{final_long_volume}"
        
        # 验证盈亏情况
        profit_loss = final_balance - initial_balance
        print(f"交易盈亏: {profit_loss:.2f}")

    def test_buy_order_with_position_check(self, trading_session):
        """买入开仓测试 - 验证持仓变化"""
        service = trading_session
        
        # 查询开仓前持仓
        before_position = service.query_investor_position(
            broker_id=service.connection_config['broker_id'],
            investor_id=service.connection_config['investor_id']
        )
        assert before_position.get('success'), "开仓前持仓查询失败"
        
        before_positions = before_position.get('data', [])
        before_if_position = None
        for pos in before_positions:
            if pos.get('instrument_id') == 'IF2512':
                before_if_position = pos
                break
        
        before_long_volume = before_if_position.get('long_volume', 0) if before_if_position else 0
        
        # 执行买入开仓
        buy_order = {
            "instrument_id": "IF2512",
            "direction": "买",
            "offset_flag": "开仓",
            "price": 3500.0,
            "volume": 2
        }
        
        result = service.insert_order_direct(**buy_order)
        assert result is not None, "买入订单提交失败"
        
        # 等待成交
        time.sleep(3)
        
        # 查询开仓后持仓
        after_position = service.query_investor_position(
            broker_id=service.connection_config['broker_id'],
            investor_id=service.connection_config['investor_id']
        )
        assert after_position.get('success'), "开仓后持仓查询失败"
        
        after_positions = after_position.get('data', [])
        after_if_position = None
        for pos in after_positions:
            if pos.get('instrument_id') == 'IF2512':
                after_if_position = pos
                break
        
        assert after_if_position is not None, "开仓后应有IF2512持仓"
        after_long_volume = after_if_position.get('long_volume', 0)
        
        # 验证持仓增加
        assert after_long_volume == before_long_volume + 2, f"多头持仓应增加2手，从{before_long_volume}变为{after_long_volume}"

    def test_sell_order_with_position_check(self, trading_session):
        """卖出平仓测试 - 验证持仓变化"""
        service = trading_session
        
        # 先开仓建立持仓
        buy_order = {
            "instrument_id": "IF2512",
            "direction": "买",
            "offset_flag": "开仓",
            "price": 3500.0,
            "volume": 1
        }
        
        buy_result = service.insert_order_direct(**buy_order)
        assert buy_result is not None, "建仓订单提交失败"
        time.sleep(3)
        
        # 查询平仓前持仓
        before_close_position = service.query_investor_position(
            broker_id=service.connection_config['broker_id'],
            investor_id=service.connection_config['investor_id']
        )
        assert before_close_position.get('success'), "平仓前持仓查询失败"
        
        before_positions = before_close_position.get('data', [])
        before_if_position = None
        for pos in before_positions:
            if pos.get('instrument_id') == 'IF2512':
                before_if_position = pos
                break
        
        assert before_if_position is not None, "平仓前应有持仓"
        before_long_volume = before_if_position.get('long_volume', 0)
        
        # 执行卖出平仓
        sell_order = {
            "instrument_id": "IF2512",
            "direction": "卖", 
            "offset_flag": "平仓",
            "price": 3490.0,
            "volume": 1
        }
        
        sell_result = service.insert_order_direct(**sell_order)
        assert sell_result is not None, "平仓订单提交失败"
        
        # 等待成交
        time.sleep(3)
        
        # 查询平仓后持仓
        after_close_position = service.query_investor_position(
            broker_id=service.connection_config['broker_id'],
            investor_id=service.connection_config['investor_id']
        )
        assert after_close_position.get('success'), "平仓后持仓查询失败"
        
        after_positions = after_close_position.get('data', [])
        after_if_position = None
        for pos in after_positions:
            if pos.get('instrument_id') == 'IF2512':
                after_if_position = pos
                break
        
        after_long_volume = after_if_position.get('long_volume', 0) if after_if_position else 0
        
        # 验证持仓减少
        assert after_long_volume == before_long_volume - 1, f"多头持仓应减少1手，从{before_long_volume}变为{after_long_volume}"

    def test_account_balance_change(self, trading_session):
        """测试账户资金变化"""
        service = trading_session
        
        # 查询初始账户
        initial_account = service.query_trading_account()
        assert initial_account.get('success'), "初始账户查询失败"
        initial_balance = initial_account.get('data', {}).get('balance', 0)
        initial_available = initial_account.get('data', {}).get('available', 0)
        
        # 执行交易
        order = {
            "instrument_id": "IF2512",
            "direction": "买",
            "offset_flag": "开仓", 
            "price": 3500.0,
            "volume": 1
        }
        
        result = service.insert_order_direct(**order)
        assert result is not None, "订单提交失败"
        
        # 等待成交
        time.sleep(3)
        
        # 查询交易后账户
        after_account = service.query_trading_account()
        assert after_account.get('success'), "交易后账户查询失败"
        after_available = after_account.get('data', {}).get('available', 0)
        
        # 验证可用资金减少（用于保证金）
        margin_used = initial_available - after_available
        assert margin_used > 0, f"开仓应占用保证金，可用资金应减少，实际变化: {margin_used}"
        
        print(f"开仓占用保证金: {margin_used:.2f}")

    def test_risk_control_scenario(self, trading_session):
        """风控场景测试"""
        service = trading_session
        
        # 测试大额订单风控
        large_order = {
            "instrument_id": "IF2512",
            "direction": "买", 
            "offset_flag": "开仓",
            "price": 3500.0,
            "volume": 100  # 大量
        }
        
        result = service.insert_order_direct(**large_order)
        
        # 根据风控设置，可能成功或失败
        assert result is not None

    def test_risk_control_position_value_ratio(self, trading_session):
        """风控测试 - 多头股指期持仓合约价值占比<4%"""
        service = trading_session
        
        # 1. 查询初始账户资金
        initial_account = service.query_trading_account(
            broker_id=service.connection_config['broker_id'],
            investor_id=service.connection_config['investor_id']
        )
        assert initial_account.get('success'), "初始账户查询失败"
        total_asset = initial_account.get('data', {}).get('balance', 0)
        
        # 计算4%限制对应的最大持仓价值
        max_position_value = total_asset * 0.04
        print(f"账户总资产: {total_asset:.2f}")
        print(f"4%风控限制对应最大持仓价值: {max_position_value:.2f}")
        
        # 2. 计算IF2512合约单手价值 (假设合约乘数300，当前价格3500)
        contract_price = 3500.0
        contract_multiplier = 300
        single_contract_value = contract_price * contract_multiplier
        max_allowed_volume = int(max_position_value / single_contract_value)
        
        print(f"IF2512单手合约价值: {single_contract_value:.2f}")
        print(f"理论最大允许持仓手数: {max_allowed_volume}")
        
        # 3. 测试正常范围内的开仓（应该成功）
        normal_volume = max(1, max_allowed_volume // 2)  # 使用一半的限额
        normal_order = {
            "instrument_id": "IF2512",
            "direction": "买",
            "offset_flag": "开仓",
            "price": contract_price,
            "volume": normal_volume
        }
        
        print(f"\n测试正常开仓 {normal_volume} 手...")
        normal_result = service.insert_order_direct(**normal_order)
        assert normal_result is not None, "正常范围内开仓应该成功"
        time.sleep(3)
        
        # 验证持仓建立
        after_normal_position = service.query_investor_position(
            broker_id=service.connection_config['broker_id'],
            investor_id=service.connection_config['investor_id']
        )
        assert after_normal_position.get('success'), "开仓后持仓查询失败"
        
        current_if_position = None
        for pos in after_normal_position.get('data', []):
            if pos.get('instrument_id') == 'IF2512':
                current_if_position = pos
                break
        
        current_long_volume = current_if_position.get('long_volume', 0) if current_if_position else 0
        current_position_value = current_long_volume * single_contract_value
        current_ratio = current_position_value / total_asset
        
        print(f"当前持仓手数: {current_long_volume}")
        print(f"当前持仓价值: {current_position_value:.2f}")
        print(f"当前持仓占比: {current_ratio:.2%}")
        
        # 4. 测试超过4%限制的开仓（应该被风控拒绝）
        excess_volume = max_allowed_volume + 5  # 超出限制
        excess_order = {
            "instrument_id": "IF2512", 
            "direction": "买",
            "offset_flag": "开仓",
            "price": contract_price,
            "volume": excess_volume
        }
        
        print(f"\n测试超限开仓 {excess_volume} 手...")
        excess_result = service.insert_order_direct(**excess_order)
        
        # 根据风控实现，可能返回None或包含错误信息
        if excess_result is None:
            print("✓ 风控正确拒绝了超限订单")
        else:
            # 检查是否包含风控错误信息
            error_msg = excess_result.get('error_message', '')
            if '风控' in error_msg or '持仓' in error_msg or '比例' in error_msg:
                print(f"✓ 风控正确拒绝: {error_msg}")
            else:
                # 如果订单被接受，检查最终持仓是否仍在限制内
                time.sleep(3)
                final_position = service.query_investor_position(
                    broker_id=service.connection_config['broker_id'],
                    investor_id=service.connection_config['investor_id']
                )
                if final_position.get('success'):
                    final_if_position = None
                    for pos in final_position.get('data', []):
                        if pos.get('instrument_id') == 'IF2512':
                            final_if_position = pos
                            break
                    
                    final_long_volume = final_if_position.get('long_volume', 0) if final_if_position else 0
                    final_position_value = final_long_volume * single_contract_value
                    final_ratio = final_position_value / total_asset
                    
                    print(f"最终持仓手数: {final_long_volume}")
                    print(f"最终持仓占比: {final_ratio:.2%}")
                    
                    # 验证风控是否生效
                    assert final_ratio < 0.04, f"风控失效！持仓占比{final_ratio:.2%}超过4%限制"
        
        # 5. 清理测试持仓
        if current_long_volume > 0:
            cleanup_order = {
                "instrument_id": "IF2512",
                "direction": "卖",
                "offset_flag": "平仓", 
                "price": contract_price - 10,  # 稍低价格确保成交
                "volume": current_long_volume
            }
            
            cleanup_result = service.insert_order_direct(**cleanup_order)
            if cleanup_result:
                time.sleep(3)
                print("✓ 测试持仓已清理")

    def test_risk_control_incremental_position(self, trading_session):
        """风控测试 - 逐步增加持仓直到触发4%限制"""
        service = trading_session
        
        # 查询账户资金
        account = service.query_trading_account(
            broker_id=service.connection_config['broker_id'],
            investor_id=service.connection_config['investor_id']
        )
        assert account.get('success'), "账户查询失败"
        total_asset = account.get('data', {}).get('balance', 0)
        
        # 合约参数
        contract_price = 3500.0
        contract_multiplier = 300
        single_contract_value = contract_price * contract_multiplier
        max_position_value = total_asset * 0.04
        
        print(f"开始逐步建仓测试...")
        print(f"单手价值: {single_contract_value:.2f}, 4%限制: {max_position_value:.2f}")
        
        current_volume = 0
        step = 1  # 每次增加1手
        
        while True:
            # 计算下一步持仓价值
            next_volume = current_volume + step
            next_position_value = next_volume * single_contract_value
            next_ratio = next_position_value / total_asset
            
            print(f"\n尝试开仓至 {next_volume} 手 (占比: {next_ratio:.2%})")
            
            if next_ratio >= 0.04:
                print("预期将触发4%风控限制")
            
            # 执行开仓
            order = {
                "instrument_id": "IF2512",
                "direction": "买", 
                "offset_flag": "开仓",
                "price": contract_price,
                "volume": step
            }
            
            result = service.insert_order_direct(**order)
            
            if result is None or (isinstance(result, dict) and result.get('error_message')):
                print(f"✓ 风控在 {next_volume} 手时生效，阻止了超限开仓")
                break
            
            time.sleep(2)
            current_volume = next_volume
            
            # 验证实际持仓
            position = service.query_investor_position()
            if position.get('success'):
                actual_volume = 0
                for pos in position.get('data', []):
                    if pos.get('instrument_id') == 'IF2512':
                        actual_volume = pos.get('long_volume', 0)
                        break
                
                actual_ratio = (actual_volume * single_contract_value) / total_asset
                print(f"实际持仓: {actual_volume} 手 (占比: {actual_ratio:.2%})")
                
                if actual_ratio >= 0.04:
                    print("⚠ 持仓占比已达到或超过4%限制")
                    break
            
            # 安全检查，避免无限循环
            if current_volume > 50:
                print("达到测试上限，停止建仓")
                break
        
        print(f"测试完成，最终持仓: {current_volume} 手")

    @pytest.mark.parametrize("instrument_id,exchange_id,price,volume", [
        # 股指期货测试组合
        ("IF2512", "CFFEX", 3500.0, 1),
        ("IF2512", "CFFEX", 3520.0, 2),
        ("IF2512", "CFFEX", 3480.0, 1),
        
        # 商品期货测试组合  
        ("rb2501", "SHFE", 3500.0, 5),
        ("rb2501", "SHFE", 3520.0, 10),
        ("rb2501", "SHFE", 3480.0, 3),
        
        # 其他合约
        ("cu2501", "SHFE", 75000.0, 1),
        ("au2512", "SHFE", 520.0, 1),
    ])
    def test_parametrized_trading_combinations(self, trading_session, instrument_id, exchange_id, price, volume):
        """参数化测试不同合约、价格和数量组合"""
        service = trading_session
        
        print(f"\n测试组合: {instrument_id} @ {price} x {volume}手")
        
        # 1. 查询合约信息验证
        instrument_result = service.query_instrument(instrument_id=instrument_id)
        assert instrument_result.get('success'), f"合约{instrument_id}查询失败"
        
        # 2. 查询初始持仓
        initial_position = service.query_investor_position()
        assert initial_position.get('success'), "初始持仓查询失败"
        
        initial_positions = initial_position.get('data', [])
        initial_volume = 0
        for pos in initial_positions:
            if pos.get('instrument_id') == instrument_id:
                initial_volume = pos.get('long_volume', 0)
                break
        
        # 3. 执行买入开仓
        buy_order = {
            "instrument_id": instrument_id,
            "exchange_id": exchange_id,
            "direction": "买",
            "offset_flag": "开仓",
            "price": price,
            "volume": volume
        }
        
        buy_result = service.insert_order_direct(**buy_order)
        assert buy_result is not None, f"买入订单提交失败: {instrument_id}"
        
        # 等待成交
        time.sleep(3)
        
        # 4. 验证持仓变化
        after_position = service.query_investor_position()
        assert after_position.get('success'), "开仓后持仓查询失败"
        
        after_positions = after_position.get('data', [])
        after_volume = 0
        for pos in after_positions:
            if pos.get('instrument_id') == instrument_id:
                after_volume = pos.get('long_volume', 0)
                break
        
        # 验证持仓增加
        expected_volume = initial_volume + volume
        assert after_volume == expected_volume, f"{instrument_id}持仓应从{initial_volume}增加到{expected_volume}，实际为{after_volume}"
        
        print(f"✓ {instrument_id}持仓验证成功: {initial_volume} -> {after_volume}")

    @pytest.mark.parametrize("direction,offset_flag", [
        ("买", "开仓"),
        ("卖", "开仓"), 
        ("买", "平仓"),
        ("卖", "平仓"),
    ])
    def test_direction_offset_combinations(self, trading_session, direction, offset_flag):
        """测试不同方向和开平仓组合"""
        service = trading_session
        
        print(f"\n测试交易方向: {direction} {offset_flag}")
        
        # 如果是平仓，先建立持仓
        if offset_flag == "平仓":
            # 先开仓建立持仓
            open_direction = "卖" if direction == "买" else "买"
            open_order = {
                "instrument_id": "IF2512",
                "direction": open_direction,
                "offset_flag": "开仓",
                "price": 3500.0,
                "volume": 1
            }
            
            open_result = service.insert_order_direct(**open_order)
            assert open_result is not None, f"建仓失败: {open_direction}"
            time.sleep(3)
        
        # 执行目标交易
        order = {
            "instrument_id": "IF2512",
            "direction": direction,
            "offset_flag": offset_flag,
            "price": 3500.0 if direction == "买" else 3490.0,
            "volume": 1
        }
        
        result = service.insert_order_direct(**order)
        assert result is not None, f"订单提交失败: {direction} {offset_flag}"
        
        print(f"✓ {direction} {offset_flag} 订单提交成功")

    @pytest.mark.parametrize("volume", [1, 2, 5, 10])
    def test_volume_scaling(self, trading_session, volume):
        """测试不同数量规模"""
        service = trading_session
        
        print(f"\n测试交易数量: {volume}手")
        
        # 查询账户资金
        account = service.query_trading_account()
        assert account.get('success'), "账户查询失败"
        
        available = account.get('data', {}).get('available', 0)
        
        # 估算所需保证金 (IF2512约需要70万/手)
        estimated_margin = volume * 700000
        
        if available < estimated_margin:
            pytest.skip(f"可用资金{available:.0f}不足，需要{estimated_margin:.0f}")
        
        # 执行交易
        order = {
            "instrument_id": "IF2512",
            "direction": "买",
            "offset_flag": "开仓", 
            "price": 3500.0,
            "volume": volume
        }
        
        result = service.insert_order_direct(**order)
        assert result is not None, f"{volume}手订单提交失败"
        
        print(f"✓ {volume}手订单提交成功")

    @pytest.mark.parametrize("price_offset", [-50, -20, -10, 0, 10, 20, 50])
    def test_price_variations(self, trading_session, price_offset):
        """测试不同价格偏移"""
        service = trading_session
        
        base_price = 3500.0
        test_price = base_price + price_offset
        
        print(f"\n测试价格: {test_price} (偏移: {price_offset:+d})")
        
        order = {
            "instrument_id": "IF2512",
            "direction": "买",
            "offset_flag": "开仓",
            "price": test_price,
            "volume": 1
        }
        
        result = service.insert_order_direct(**order)
        assert result is not None, f"价格{test_price}订单提交失败"
        
        print(f"✓ 价格{test_price}订单提交成功")

    @pytest.mark.parametrize("instrument_config", [
        {"id": "IF2512", "exchange": "CFFEX", "multiplier": 300, "margin_rate": 0.12},
        {"id": "rb2501", "exchange": "SHFE", "multiplier": 10, "margin_rate": 0.08},
        {"id": "cu2501", "exchange": "SHFE", "multiplier": 5, "margin_rate": 0.07},
    ])
    def test_contract_specific_trading(self, trading_session, instrument_config):
        """测试特定合约的交易特性"""
        service = trading_session
        
        instrument_id = instrument_config["id"]
        exchange_id = instrument_config["exchange"]
        
        print(f"\n测试合约: {instrument_id} ({exchange_id})")
        
        # 查询合约详情
        instrument_result = service.query_instrument(instrument_id=instrument_id)
        assert instrument_result.get('success'), f"合约{instrument_id}查询失败"
        
        # 根据合约特性调整交易参数
        if instrument_id.startswith("IF"):
            # 股指期货
            price = 3500.0
            volume = 1
        elif instrument_id.startswith("rb"):
            # 螺纹钢
            price = 3500.0
            volume = 5
        elif instrument_id.startswith("cu"):
            # 铜
            price = 75000.0
            volume = 1
        else:
            price = 3500.0
            volume = 1
        
        order = {
            "instrument_id": instrument_id,
            "exchange_id": exchange_id,
            "direction": "买",
            "offset_flag": "开仓",
            "price": price,
            "volume": volume
        }
        
        result = service.insert_order_direct(**order)
        assert result is not None, f"合约{instrument_id}交易失败"
        
        print(f"✓ 合约{instrument_id}交易成功")

    def test_trading_with_verification(self, trading_session, test_logger):
        """带验证的交易测试"""
        service = trading_session
        
        # 记录初始状态
        initial_account = service.query_trading_account()
        initial_position = service.query_investor_position()
        
        initial_state = {
            "account": initial_account.get('data', {}),
            "positions": self._format_positions(initial_position.get('data', []))
        }
        
        # 执行交易
        trading_actions = []
        
        buy_order = {
            "instrument_id": "IF2512",
            "direction": "买",
            "offset_flag": "开仓",
            "price": 3500.0,
            "volume": 1
        }
        
        result = service.insert_order_direct(**buy_order)
        trading_actions.append(buy_order)
        
        time.sleep(3)
        
        # 记录最终状态
        final_account = service.query_trading_account()
        final_position = service.query_investor_position()
        
        final_state = {
            "account": final_account.get('data', {}),
            "positions": self._format_positions(final_position.get('data', []))
        }
        
        # 记录验证结果
        verification = test_logger.log_trading_verification(
            "买入开仓测试",
            initial_state,
            final_state,
            trading_actions
        )
        
        # 断言验证成功
        assert verification["verification"]["success"], "交易验证失败"

    def _format_positions(self, positions):
        """格式化持仓数据"""
        formatted = {}
        for pos in positions:
            instrument = pos.get('instrument_id')
            if instrument:
                formatted[instrument] = {
                    'long': pos.get('long_volume', 0),
                    'short': pos.get('short_volume', 0)
                }
        return formatted

class TestComplexScenarios:
    """复杂场景的pytest测试"""
    
    @pytest.fixture(scope="session")
    def test_runner(self):
        """测试运行器fixture"""
        return AutomatedTestRunner()
    
    @pytest.mark.integration
    def test_full_trading_cycle(self, test_runner):
        """完整交易周期测试"""
        test_runner.exec_config.mode = "full"
        results = test_runner._execute_full_plan()
        
        # 验证整体成功率
        overall_success = sum(r.get('summary', {}).get('overall_success_rate', 0) 
                            for r in results) / len(results)
        assert overall_success >= 70, f"整体成功率过低: {overall_success}%"
    
    @pytest.mark.parametrize("scenario_id", [
        "basic_trading_test",
        "risk_control_test", 
        "direct_complete_test"
    ])
    def test_individual_scenarios(self, test_runner, scenario_id):
        """单个场景测试"""
        test_runner.exec_config.mode = "scenario"
        test_runner.exec_config.target = scenario_id
        results = test_runner._execute_single_scenario()
        
        assert results['summary']['overall_success_rate'] >= 50
