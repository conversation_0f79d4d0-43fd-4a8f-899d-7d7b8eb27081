#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证规则执行器
专门处理futures_validations.csv中定义的验证条件
"""

import re
import json
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime, date
from decimal import Decimal

from futures_config_reader import ValidationRule


@dataclass
class ValidationContext:
    """验证上下文"""
    test_case_id: str
    step_id: str
    step_name: str
    execution_time: datetime
    actual_result: Dict[str, Any]
    context_data: Dict[str, Any]


class ValidationExecutor:
    """验证规则执行器"""
    
    def __init__(self):
        self.validation_history = []
        self.context_stack = []
    
    def execute_validations(self, 
                          actual_result: Dict[str, Any], 
                          validation_rules: List[ValidationRule],
                          context: ValidationContext) -> List[Dict[str, Any]]:
        """执行验证规则列表"""
        validation_results = []
        
        print(f"    执行 {len(validation_rules)} 个验证规则:")
        
        for i, rule in enumerate(validation_rules, 1):
            print(f"      [{i}] {rule.validation_name}")
            
            try:
                result = self.execute_single_validation(actual_result, rule, context)
                validation_results.append(result)
                
                # 显示验证结果
                status_symbol = "✓" if result['status'] == '通过' else "✗"
                print(f"          {status_symbol} {result['status']}")
                
                if result['status'] == '失败':
                    print(f"          原因: {result['message']}")
                    print(f"          期望: {result['expected']}, 实际: {result['actual']}")
                
            except Exception as e:
                error_result = {
                    'rule_id': rule.validation_id,
                    'rule_name': rule.validation_name,
                    'status': '错误',
                    'message': f'验证执行异常: {e}',
                    'field_name': rule.field_name,
                    'expected': rule.expected_value,
                    'actual': None,
                    'operator': rule.operator,
                    'validation_type': rule.validation_type,
                    'execution_time': context.execution_time.isoformat()
                }
                validation_results.append(error_result)
                print(f"          ✗ 错误: {e}")
        
        # 记录验证历史
        self.validation_history.append({
            'context': context,
            'results': validation_results,
            'timestamp': datetime.now()
        })
        
        return validation_results
    
    def execute_single_validation(self, 
                                actual_result: Dict[str, Any], 
                                rule: ValidationRule,
                                context: ValidationContext) -> Dict[str, Any]:
        """执行单个验证规则"""
        
        # 获取字段值
        field_value = self._get_field_value(actual_result, rule.field_name, context)
        
        # 执行验证逻辑
        validation_passed = self._execute_validation_logic(field_value, rule, context)
        
        # 构建结果
        result = {
            'rule_id': rule.validation_id,
            'rule_name': rule.validation_name,
            'status': '通过' if validation_passed else '失败',
            'message': '' if validation_passed else rule.error_message,
            'field_name': rule.field_name,
            'expected': rule.expected_value,
            'actual': field_value,
            'operator': rule.operator,
            'validation_type': rule.validation_type,
            'is_required': rule.is_required,
            'execution_time': context.execution_time.isoformat()
        }
        
        # 如果是必需验证且失败，添加严重性标记
        if not validation_passed and rule.is_required:
            result['severity'] = 'critical'
        else:
            result['severity'] = 'normal'
        
        return result
    
    def _get_field_value(self, 
                        actual_result: Dict[str, Any], 
                        field_path: str, 
                        context: ValidationContext) -> Any:
        """获取字段值，支持多种访问方式"""
        
        # 1. 直接字段访问
        if '.' not in field_path:
            return actual_result.get(field_path)
        
        # 2. 嵌套字段访问 (如: account.balance)
        try:
            value = actual_result
            for key in field_path.split('.'):
                if isinstance(value, dict):
                    value = value.get(key)
                else:
                    return None
            return value
        except:
            pass
        
        # 3. 上下文数据访问 (如: context.account_info.available_fund)
        if field_path.startswith('context.'):
            try:
                context_path = field_path[8:]  # 移除 'context.'
                value = context.context_data
                for key in context_path.split('.'):
                    if isinstance(value, dict):
                        value = value.get(key)
                    else:
                        return None
                return value
            except:
                pass
        
        # 4. 计算字段 (如: margin_used + required_margin)
        if '+' in field_path or '-' in field_path or '*' in field_path or '/' in field_path:
            return self._calculate_field_value(field_path, actual_result, context)
        
        return None
    
    def _calculate_field_value(self, 
                             expression: str, 
                             actual_result: Dict[str, Any], 
                             context: ValidationContext) -> Optional[float]:
        """计算字段值表达式"""
        try:
            # 简单的数学表达式计算
            # 替换字段名为实际值
            calc_expr = expression
            
            # 查找所有字段名并替换
            field_pattern = r'[a-zA-Z_][a-zA-Z0-9_]*'
            fields = re.findall(field_pattern, expression)
            
            for field in fields:
                field_value = self._get_field_value(actual_result, field, context)
                if field_value is not None:
                    calc_expr = calc_expr.replace(field, str(float(field_value)))
                else:
                    return None
            
            # 安全计算
            allowed_chars = set('0123456789+-*/.() ')
            if all(c in allowed_chars for c in calc_expr):
                return eval(calc_expr)
            
        except:
            pass
        
        return None
    
    def _execute_validation_logic(self, 
                                actual_value: Any, 
                                rule: ValidationRule,
                                context: ValidationContext) -> bool:
        """执行验证逻辑"""
        
        validation_type = rule.validation_type
        operator = rule.operator
        expected_value = rule.expected_value
        
        # 处理空值情况
        if actual_value is None:
            if validation_type == "空值检查":
                return operator == "=" and expected_value in [None, "null", ""]
            elif validation_type == "非空检查":
                return False
            elif rule.is_required:
                return False
            else:
                return True  # 非必需字段为空时通过
        
        # 根据验证类型执行相应逻辑
        if validation_type == "数值检查":
            return self._validate_numeric(actual_value, operator, expected_value)
        elif validation_type == "字符串检查":
            return self._validate_string(actual_value, operator, expected_value)
        elif validation_type == "布尔检查":
            return self._validate_boolean(actual_value, operator, expected_value)
        elif validation_type == "非空检查":
            return self._validate_not_null(actual_value, operator, expected_value)
        elif validation_type == "空值检查":
            return self._validate_null(actual_value, operator, expected_value)
        elif validation_type == "范围检查":
            return self._validate_range(actual_value, operator, expected_value)
        elif validation_type == "包含检查":
            return self._validate_contains(actual_value, operator, expected_value)
        elif validation_type == "正则检查":
            return self._validate_regex(actual_value, operator, expected_value)
        elif validation_type == "时间检查":
            return self._validate_time(actual_value, operator, expected_value)
        elif validation_type == "枚举检查":
            return self._validate_enum(actual_value, operator, expected_value)
        else:
            print(f"        警告: 未知验证类型 {validation_type}")
            return True
    
    def _validate_numeric(self, actual: Any, operator: str, expected: Any) -> bool:
        """数值验证"""
        try:
            actual_num = self._to_number(actual)
            expected_num = self._to_number(expected)
            
            if actual_num is None or expected_num is None:
                return False
            
            if operator == '>':
                return actual_num > expected_num
            elif operator == '>=':
                return actual_num >= expected_num
            elif operator == '<':
                return actual_num < expected_num
            elif operator == '<=':
                return actual_num <= expected_num
            elif operator == '=' or operator == '==':
                return abs(actual_num - expected_num) < 1e-6  # 浮点数比较
            elif operator == '!=' or operator == '<>':
                return abs(actual_num - expected_num) >= 1e-6
            else:
                return False
        except:
            return False
    
    def _validate_string(self, actual: Any, operator: str, expected: Any) -> bool:
        """字符串验证"""
        try:
            actual_str = str(actual) if actual is not None else ""
            expected_str = str(expected) if expected is not None else ""
            
            if operator == '=' or operator == '==':
                return actual_str == expected_str
            elif operator == '!=' or operator == '<>':
                return actual_str != expected_str
            elif operator == 'contains':
                return expected_str in actual_str
            elif operator == 'not_contains':
                return expected_str not in actual_str
            elif operator == 'starts_with':
                return actual_str.startswith(expected_str)
            elif operator == 'ends_with':
                return actual_str.endswith(expected_str)
            else:
                return False
        except:
            return False
    
    def _validate_boolean(self, actual: Any, operator: str, expected: Any) -> bool:
        """布尔值验证"""
        try:
            actual_bool = self._to_boolean(actual)
            expected_bool = self._to_boolean(expected)
            
            if operator == '=' or operator == '==':
                return actual_bool == expected_bool
            elif operator == '!=' or operator == '<>':
                return actual_bool != expected_bool
            else:
                return False
        except:
            return False
    
    def _validate_not_null(self, actual: Any, operator: str, expected: Any) -> bool:
        """非空验证"""
        if operator == '!=' or operator == '<>':
            return actual is not None and actual != '' and actual != 'null'
        else:
            return False
    
    def _validate_null(self, actual: Any, operator: str, expected: Any) -> bool:
        """空值验证"""
        if operator == '=' or operator == '==':
            return actual is None or actual == '' or actual == 'null'
        else:
            return False
    
    def _validate_range(self, actual: Any, operator: str, expected: Any) -> bool:
        """范围验证"""
        try:
            actual_num = self._to_number(actual)
            if actual_num is None:
                return False
            
            # expected格式: "min,max" 或 [min, max]
            if isinstance(expected, str) and ',' in expected:
                min_val, max_val = map(float, expected.split(','))
            elif isinstance(expected, (list, tuple)) and len(expected) == 2:
                min_val, max_val = float(expected[0]), float(expected[1])
            else:
                return False
            
            if operator == 'between' or operator == 'in_range':
                return min_val <= actual_num <= max_val
            elif operator == 'not_between' or operator == 'not_in_range':
                return not (min_val <= actual_num <= max_val)
            else:
                return False
        except:
            return False
    
    def _validate_contains(self, actual: Any, operator: str, expected: Any) -> bool:
        """包含检查"""
        try:
            if operator == 'contains' or operator == 'in':
                if isinstance(actual, (list, tuple)):
                    return expected in actual
                else:
                    return str(expected) in str(actual)
            elif operator == 'not_contains' or operator == 'not_in':
                if isinstance(actual, (list, tuple)):
                    return expected not in actual
                else:
                    return str(expected) not in str(actual)
            else:
                return False
        except:
            return False
    
    def _validate_regex(self, actual: Any, operator: str, expected: Any) -> bool:
        """正则表达式验证"""
        try:
            actual_str = str(actual) if actual is not None else ""
            pattern = str(expected)
            
            if operator == 'matches' or operator == '~':
                return bool(re.match(pattern, actual_str))
            elif operator == 'not_matches' or operator == '!~':
                return not bool(re.match(pattern, actual_str))
            elif operator == 'search':
                return bool(re.search(pattern, actual_str))
            else:
                return False
        except:
            return False
    
    def _validate_time(self, actual: Any, operator: str, expected: Any) -> bool:
        """时间验证"""
        try:
            actual_time = self._to_datetime(actual)
            expected_time = self._to_datetime(expected)
            
            if actual_time is None or expected_time is None:
                return False
            
            if operator == '>' or operator == 'after':
                return actual_time > expected_time
            elif operator == '>=' or operator == 'after_or_equal':
                return actual_time >= expected_time
            elif operator == '<' or operator == 'before':
                return actual_time < expected_time
            elif operator == '<=' or operator == 'before_or_equal':
                return actual_time <= expected_time
            elif operator == '=' or operator == '==':
                return actual_time == expected_time
            else:
                return False
        except:
            return False
    
    def _validate_enum(self, actual: Any, operator: str, expected: Any) -> bool:
        """枚举值验证"""
        try:
            # expected格式: "value1,value2,value3" 或 ["value1", "value2", "value3"]
            if isinstance(expected, str):
                enum_values = [v.strip() for v in expected.split(',')]
            elif isinstance(expected, (list, tuple)):
                enum_values = [str(v) for v in expected]
            else:
                enum_values = [str(expected)]
            
            actual_str = str(actual) if actual is not None else ""
            
            if operator == 'in' or operator == '=':
                return actual_str in enum_values
            elif operator == 'not_in' or operator == '!=':
                return actual_str not in enum_values
            else:
                return False
        except:
            return False
    
    def _to_number(self, value: Any) -> Optional[float]:
        """转换为数字"""
        if value is None:
            return None
        
        try:
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, Decimal):
                return float(value)
            elif isinstance(value, str):
                # 移除千分位分隔符
                clean_value = value.replace(',', '').strip()
                return float(clean_value)
            else:
                return float(value)
        except:
            return None
    
    def _to_boolean(self, value: Any) -> Optional[bool]:
        """转换为布尔值"""
        if value is None:
            return None
        
        if isinstance(value, bool):
            return value
        elif isinstance(value, str):
            lower_value = value.lower().strip()
            if lower_value in ['true', '1', 'yes', 'on', '是', '真']:
                return True
            elif lower_value in ['false', '0', 'no', 'off', '否', '假']:
                return False
            else:
                return None
        elif isinstance(value, (int, float)):
            return bool(value)
        else:
            return bool(value)
    
    def _to_datetime(self, value: Any) -> Optional[datetime]:
        """转换为日期时间"""
        if value is None:
            return None
        
        if isinstance(value, datetime):
            return value
        elif isinstance(value, date):
            return datetime.combine(value, datetime.min.time())
        elif isinstance(value, str):
            # 尝试多种日期格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%H:%M:%S',
                '%Y/%m/%d %H:%M:%S',
                '%Y/%m/%d'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(value, fmt)
                except:
                    continue
            
            return None
        else:
            return None
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """获取验证汇总"""
        if not self.validation_history:
            return {}
        
        total_validations = 0
        passed_validations = 0
        failed_validations = 0
        error_validations = 0
        critical_failures = 0
        
        for history_item in self.validation_history:
            for result in history_item['results']:
                total_validations += 1
                
                if result['status'] == '通过':
                    passed_validations += 1
                elif result['status'] == '失败':
                    failed_validations += 1
                    if result.get('severity') == 'critical':
                        critical_failures += 1
                else:
                    error_validations += 1
        
        success_rate = (passed_validations / total_validations * 100) if total_validations > 0 else 0
        
        return {
            'total_validations': total_validations,
            'passed_validations': passed_validations,
            'failed_validations': failed_validations,
            'error_validations': error_validations,
            'critical_failures': critical_failures,
            'success_rate': success_rate,
            'validation_sessions': len(self.validation_history)
        }