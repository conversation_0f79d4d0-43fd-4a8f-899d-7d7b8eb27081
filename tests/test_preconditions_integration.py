#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前置条件集成测试
测试前置条件在实际场景中的组合使用
"""

import pytest
from futures_config_reader import FuturesConfigReader
from futures_test_executor import FuturesTestExecutor


class TestPreconditionsIntegration:
    """前置条件集成测试类"""
    
    @pytest.fixture(scope="class")
    def test_executor(self):
        """测试执行器fixture"""
        config_reader = FuturesConfigReader()
        return FuturesTestExecutor(config_reader)
    
    def test_basic_trading_preconditions(self, test_executor):
        """测试基础交易前置条件组合"""
        basic_conditions = [
            "PRECOND_001",  # 账户资金充足
            "PRECOND_002",  # 具有期货交易权限
            "PRECOND_003",  # 在交易时间内
            "PRECOND_004",  # 合约处于正常交易状态
            "PRECOND_005"   # 系统正常运行
        ]
        
        results = {}
        for condition_id in basic_conditions:
            result = test_executor.execute_precondition(condition_id)
            results[condition_id] = result
            precondition = test_executor.config_reader.get_precondition(condition_id)
            print(f"{condition_id} - {precondition.condition_name}: {'✓' if result else '✗'}")
        
        # 基础交易条件应该大部分通过
        passed_count = sum(1 for result in results.values() if result)
        total_count = len(basic_conditions)
        pass_rate = passed_count / total_count
        
        print(f"基础交易前置条件通过率: {pass_rate:.1%} ({passed_count}/{total_count})")
        assert pass_rate >= 0.6, "基础交易前置条件通过率应该至少60%"
    
    def test_stock_index_futures_preconditions(self, test_executor):
        """测试股指期货特定前置条件"""
        stock_index_conditions = [
            "PRECOND_001",  # 账户资金充足
            "PRECOND_011",  # 具有股指期货权限
            "PRECOND_004",  # IF2501合约正常
            "PRECOND_007"   # 价格数据正常
        ]
        
        results = {}
        for condition_id in stock_index_conditions:
            result = test_executor.execute_precondition(condition_id)
            results[condition_id] = result
            precondition = test_executor.config_reader.get_precondition(condition_id)
            print(f"{condition_id} - {precondition.condition_name}: {'✓' if result else '✗'}")
        
        # 检查股指期货权限
        assert "PRECOND_011" in results, "应该检查股指期货权限"
        print(f"股指期货权限检查: {'通过' if results['PRECOND_011'] else '失败'}")
    
    def test_commodity_futures_preconditions(self, test_executor):
        """测试商品期货特定前置条件"""
        commodity_conditions = [
            "PRECOND_001",  # 账户资金充足
            "PRECOND_012",  # 具有商品期货权限
            "PRECOND_013"   # 螺纹钢合约正常
        ]
        
        results = {}
        for condition_id in commodity_conditions:
            result = test_executor.execute_precondition(condition_id)
            results[condition_id] = result
            precondition = test_executor.config_reader.get_precondition(condition_id)
            print(f"{condition_id} - {precondition.condition_name}: {'✓' if result else '✗'}")
        
        # 检查商品期货权限
        assert "PRECOND_012" in results, "应该检查商品期货权限"
        print(f"商品期货权限检查: {'通过' if results['PRECOND_012'] else '失败'}")
    
    def test_risk_control_preconditions(self, test_executor):
        """测试风控相关前置条件"""
        risk_conditions = [
            "PRECOND_006",  # 风控参数配置正确
            "PRECOND_015",  # 保证金监控正常
            "PRECOND_016"   # 风控系统激活
        ]
        
        results = {}
        for condition_id in risk_conditions:
            result = test_executor.execute_precondition(condition_id)
            results[condition_id] = result
            precondition = test_executor.config_reader.get_precondition(condition_id)
            print(f"{condition_id} - {precondition.condition_name}: {'✓' if result else '✗'}")
        
        # 风控系统应该正常
        print("风控系统状态检查完成")
    
    def test_system_infrastructure_preconditions(self, test_executor):
        """测试系统基础设施前置条件"""
        infrastructure_conditions = [
            "PRECOND_005",  # 系统正常运行
            "PRECOND_018",  # 网络连接正常
            "PRECOND_019",  # 数据同步正常
            "PRECOND_020"   # 清算系统正常
        ]
        
        results = {}
        for condition_id in infrastructure_conditions:
            result = test_executor.execute_precondition(condition_id)
            results[condition_id] = result
            precondition = test_executor.config_reader.get_precondition(condition_id)
            print(f"{condition_id} - {precondition.condition_name}: {'✓' if result else '✗'}")
        
        # 系统基础设施应该大部分正常
        passed_count = sum(1 for result in results.values() if result)
        total_count = len(infrastructure_conditions)
        pass_rate = passed_count / total_count
        
        print(f"系统基础设施前置条件通过率: {pass_rate:.1%} ({passed_count}/{total_count})")
        assert pass_rate >= 0.5, "系统基础设施前置条件通过率应该至少50%"
    
    def test_insufficient_fund_scenario_preconditions(self, test_executor):
        """测试资金不足场景的前置条件"""
        # 先设置低资金
        low_fund_result = test_executor.execute_precondition("PRECOND_008")
        print(f"设置低资金: {'成功' if low_fund_result else '失败'}")
        
        # 然后检查资金充足性（应该失败）
        fund_check_result = test_executor.execute_precondition("PRECOND_001")
        print(f"资金充足检查: {'通过' if fund_check_result else '失败'}")
        
        # 在低资金设置后，资金充足检查应该失败
        if low_fund_result:
            assert not fund_check_result, "设置低资金后，资金充足检查应该失败"
            print("✓ 资金不足场景前置条件验证通过")
    
    def test_precondition_dependencies(self, test_executor):
        """测试前置条件之间的依赖关系"""
        # 某些前置条件可能依赖其他条件
        # 例如：交易权限依赖于账户状态正常
        
        account_status = test_executor.execute_precondition("PRECOND_009")
        trading_permission = test_executor.execute_precondition("PRECOND_002")
        
        print(f"账户状态正常: {'✓' if account_status else '✗'}")
        print(f"期货交易权限: {'✓' if trading_permission else '✗'}")
        
        # 如果账户状态异常，交易权限也可能受影响
        if not account_status:
            print("⚠ 账户状态异常可能影响交易权限")
    
    def test_all_preconditions_summary(self, test_executor):
        """测试所有前置条件的汇总报告"""
        all_condition_ids = [
            "PRECOND_001", "PRECOND_002", "PRECOND_003", "PRECOND_004", "PRECOND_005",
            "PRECOND_006", "PRECOND_007", "PRECOND_008", "PRECOND_009", "PRECOND_010",
            "PRECOND_011", "PRECOND_012", "PRECOND_013", "PRECOND_014", "PRECOND_015",
            "PRECOND_016", "PRECOND_017", "PRECOND_018", "PRECOND_019", "PRECOND_020"
        ]
        
        results = {}
        categories = {}
        
        for condition_id in all_condition_ids:
            precondition = test_executor.config_reader.get_precondition(condition_id)
            if precondition:
                result = test_executor.execute_precondition(condition_id)
                results[condition_id] = result
                
                # 按类型分类
                condition_type = precondition.condition_type
                if condition_type not in categories:
                    categories[condition_type] = []
                categories[condition_type].append((condition_id, result))
        
        # 生成汇总报告
        print("\n" + "="*60)
        print("前置条件检查汇总报告")
        print("="*60)
        
        total_passed = sum(1 for result in results.values() if result)
        total_count = len(results)
        overall_pass_rate = total_passed / total_count if total_count > 0 else 0
        
        print(f"总体通过率: {overall_pass_rate:.1%} ({total_passed}/{total_count})")
        print()
        
        # 按类型显示结果
        for condition_type, conditions in categories.items():
            passed = sum(1 for _, result in conditions if result)
            total = len(conditions)
            pass_rate = passed / total if total > 0 else 0
            
            print(f"{condition_type}: {pass_rate:.1%} ({passed}/{total})")
            for condition_id, result in conditions:
                precondition = test_executor.config_reader.get_precondition(condition_id)
                status = "✓" if result else "✗"
                print(f"  {status} {condition_id}: {precondition.condition_name}")
        
        print("="*60)
        
        # 基本断言
        assert total_count > 0, "应该至少有一个前置条件"
        assert overall_pass_rate >= 0.3, "总体通过率应该至少30%"