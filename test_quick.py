#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
用于开发过程中的快速验证
"""

import sys
import subprocess
from pathlib import Path


def run_quick_test():
    """运行快速测试"""
    project_root = Path(__file__).parent
    
    # 设置环境
    import os
    os.environ['PYTHONPATH'] = f"{project_root}:{project_root}/trader"
    
    print("🚀 运行快速测试...")
    print("=" * 50)
    
    # 快速测试用例列表
    quick_tests = [
        # 基础连接测试
        "tests/test_preconditions.py::TestPreconditions::test_precond_005_system_normal",
        
        # 账户查询测试
        "tests/test_query_functions.py::TestQueryFunctions::test_query_trading_account",
        
        # 基础前置条件测试
        "tests/test_preconditions.py::TestPreconditions::test_precond_001_account_fund_sufficient",
        
        # 合约查询测试
        "tests/test_query_functions.py::TestQueryFunctions::test_query_instrument",
    ]
    
    success_count = 0
    total_count = len(quick_tests)
    
    for i, test in enumerate(quick_tests, 1):
        print(f"\n[{i}/{total_count}] 运行: {test}")
        print("-" * 50)
        
        try:
            result = subprocess.run([
                "python", "-m", "pytest", test, "-v", "--tb=short"
            ], cwd=project_root, capture_output=False)
            
            if result.returncode == 0:
                print(f"✓ 测试通过")
                success_count += 1
            else:
                print(f"✗ 测试失败")
                
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"快速测试完成: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有快速测试通过!")
        return True
    else:
        print("❌ 部分测试失败，请检查")
        return False


if __name__ == "__main__":
    success = run_quick_test()
    sys.exit(0 if success else 1)