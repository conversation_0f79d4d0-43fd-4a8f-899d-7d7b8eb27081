test_scenarios:
  direct_complete_test:
    scenario_id: "direct_complete_test"
    scenario_name: "直接接口完整功能测试"
    description: "测试CTP直接接口的查询、下单和回调功能"
    category: "直接接口测试"
    test_cases:
      - "direct_query_test"
      - "direct_order_test"

test_cases:
  direct_query_test:
    test_id: "direct_query_test"
    test_name: "直接接口查询测试"
    description: "测试资金账户、合约和持仓的直接查询功能"
    category: "查询功能"
    priority: "高"
    steps:
      - step_id: "query_account"
        step_name: "查询资金账户"
        action_type: "query"
        parameters:
          operation: "query_trading_account"
        expected_result:
          success: true
        validation_rules:
          - field: "success"
            operator: "equals"
            expected_value: true
            
  direct_order_test:
    test_id: "direct_order_test"
    test_name: "直接接口下单测试"
    description: "测试直接下单和回调功能"
    category: "交易功能"
    priority: "高"
    steps:
      - step_id: "direct_order"
        step_name: "执行直接下单"
        action_type: "order"
        parameters:
          instrument_id: "IF2512"
          direction: "买"
          offset_flag: "开仓"
          price: 4089.0
          volume: 1
          exchange_id: "CFFEX"
        expected_result:
          success: true