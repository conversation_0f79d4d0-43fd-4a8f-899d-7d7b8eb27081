# 期货测试执行配置
# 所有测试参数从此配置文件读取，无需交互输入

# 执行模式配置
execution:
  mode: "full"  # scenario: 执行场景, case: 执行单个案例, full: 执行完整计划
  target: ""  # 目标场景ID或案例ID
  auto_cleanup: true  # 自动执行清理动作
  generate_report: true  # 生成HTML报告
  save_json: true  # 保存JSON结果

# 环境配置
environment:
  ctp_config:
    md_front: "tcp://180.168.146.187:10131"
    trade_front: "tcp://10.141.113.102:6661"
    broker_id: "********"
    user_id: "************"
    password: "000000"
    investor_id: "************"
    app_id: "invest_smart"
    auth_code: "auth123"
    user_product_info: "product_info"
  
  system:
    timeout: 5
    retry_count: 3
    log_level: "INFO"
    enable_debug: true
  
  directories:
    data: "./data"
    result: "./result"
    plot: "./plot"
    config: "./config"

# 测试数据配置
test_data:
  account:
    initial_fund: 1000000.0  # 初始资金
    available_fund: 100000.0  # 可用资金
    margin_used: 50000.0     # 已用保证金
  
  instruments:
    IF2501:
      name: "沪深300股指期货"
      exchange: "CFFEX"
      multiplier: 300
      margin_rate: 0.12
      price_tick: 0.2
      current_price: 4000.0
    
    rb2501:
      name: "螺纹钢期货"
      exchange: "SHFE"
      multiplier: 10
      margin_rate: 0.08
      price_tick: 1.0
      current_price: 3500.0

# 报告配置
report:
  title: "CTP期货交易自动化测试报告"
  include_environment: true    # 包含环境配置
  include_step_details: true   # 包含步骤详细设计
  include_trading_config: true # 包含交易配置
  include_messages: true       # 包含报文和回文
  include_execution_check: true # 包含交易执行结果检查
  
  output:
    filename_template: "futures_test_report_{timestamp}.html"
    open_browser: false  # 生成后不自动打开浏览器

# 场景执行顺序（当mode为full时使用）
execution_order:
  - "SCENARIO_FU_001"  # 股指期货基础交易流程
  # - "SCENARIO_FU_002"  # 商品期货基础交易流程
  # - "SCENARIO_FU_003"  # 保证金风控测试
  # - "SCENARIO_FU_004"  # 维持保证金监控测试
  # - "SCENARIO_FU_005"  # 综合交易风控测试
